<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".Web3Manager">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header Card -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="🚀 Web3 Wallet"
                    android:textColor="#2E7D32"
                    android:textSize="24sp"
                    android:textStyle="bold"
                    android:gravity="center"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/tvConnectionStatus"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="🔴 Disconnected"
                    android:textColor="#D32F2F"
                    android:textSize="16sp"
                    android:gravity="center"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/tvNetworkInfo"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Network: Loading..."
                    android:textColor="#666666"
                    android:textSize="14sp"
                    android:gravity="center" />

                <TextView
                    android:id="@+id/tvBlockNumber"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Block: Loading..."
                    android:textColor="#666666"
                    android:textSize="12sp"
                    android:gravity="center"
                    android:layout_marginTop="4dp" />

            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- Wallet Connection Card -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="💼 Connect Wallet"
                    android:textColor="#1976D2"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="16dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="12dp">

                    <Button
                        android:id="@+id/btnConnectMetaMask"
                        android:layout_width="0dp"
                        android:layout_height="56dp"
                        android:layout_weight="1"
                        android:layout_marginEnd="8dp"
                        android:text="🦊 MetaMask"
                        android:textColor="#FFFFFF"
                        android:textSize="14sp"
                        android:textStyle="bold" />

                    <Button
                        android:id="@+id/btnConnectTrust"
                        android:layout_width="0dp"
                        android:layout_height="56dp"
                        android:layout_weight="1"
                        android:layout_marginStart="8dp"
                        android:text="🛡️ Trust Wallet"
                        android:textColor="#FFFFFF"
                        android:textSize="14sp"
                        android:textStyle="bold" />

                </LinearLayout>

                <TextView
                    android:id="@+id/tvWalletAddress"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Wallet: Not Connected"
                    android:textColor="#666666"
                    android:textSize="12sp"
                    android:gravity="center"
                    android:layout_marginTop="8dp"
                    android:background="#f0f0f0"
                    android:padding="8dp"
                    android:visibility="gone" />

                <Button
                    android:id="@+id/btnDisconnectWallet"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Disconnect Wallet"
                    android:textColor="#FFFFFF"
                    android:layout_marginTop="8dp"
                    android:visibility="gone" />

            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- Balance Check Card -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="💰 Check Balance"
                    android:textColor="#FF6F00"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="16dp" />

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:hint="Enter wallet address"
                    app:boxStrokeColor="#FF6F00"
                    app:hintTextColor="#FF6F00">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etWalletAddress"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="text"
                        android:textSize="14sp" />

                </com.google.android.material.textfield.TextInputLayout>

                <Button
                    android:id="@+id/btnGetBalance"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:text="Get Balance"
                    android:textColor="#FFFFFF"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="12dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:background="#f8f8f8"
                    android:padding="12dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Balance:"
                        android:textColor="#666666"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/tvBalance"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0.0000 ETH"
                        android:textColor="#2E7D32"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                </LinearLayout>

            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- Network Settings Card -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="🌐 Network Settings"
                    android:textColor="#7B1FA2"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="16dp" />

                <RadioGroup
                    android:id="@+id/rgNetwork"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp">

                    <RadioButton
                        android:id="@+id/rbSepolia"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="🧪 Sepolia Testnet (Recommended)"
                        android:textColor="#666666"
                        android:checked="true" />

                    <RadioButton
                        android:id="@+id/rbMainnet"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="🚀 Ethereum Mainnet"
                        android:textColor="#666666" />

                    <RadioButton
                        android:id="@+id/rbPolygon"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="⚡ Polygon Mainnet"
                        android:textColor="#666666" />

                </RadioGroup>

                <Button
                    android:id="@+id/btnSwitchNetwork"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Switch Network"
                    android:textColor="#FFFFFF"
                    android:textSize="14sp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginTop="12dp"
                    android:background="#f0f0f0"
                    android:padding="8dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Gas Price:"
                        android:textColor="#666666"
                        android:textSize="12sp" />

                    <TextView
                        android:id="@+id/tvGasPrice"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Loading..."
                        android:textColor="#666666"
                        android:textSize="12sp" />

                </LinearLayout>

            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- Transaction Card (Hidden initially) -->
        <androidx.cardview.widget.CardView
            android:id="@+id/cardTransaction"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:visibility="gone"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="💸 Send Transaction"
                    android:textColor="#D32F2F"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="16dp" />

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:hint="To Address"
                    app:boxStrokeColor="#D32F2F"
                    app:hintTextColor="#D32F2F">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etToAddress"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="text"
                        android:textSize="14sp" />

                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:hint="Amount (ETH)"
                    app:boxStrokeColor="#D32F2F"
                    app:hintTextColor="#D32F2F">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etAmount"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="numberDecimal"
                        android:textSize="14sp" />

                </com.google.android.material.textfield.TextInputLayout>

                <Button
                    android:id="@+id/btnSendTransaction"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:text="Send Transaction"
                    android:textColor="#FFFFFF"
                    android:textSize="16sp"
                    android:textStyle="bold" />

            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- Footer Info -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="#e8e8e8"
            android:padding="12dp"
            android:layout_marginTop="8dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="ℹ️ Web3 Integration Info"
                android:textColor="#666666"
                android:textSize="12sp"
                android:textStyle="bold"
                android:layout_marginBottom="4dp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="• Always test on Sepolia testnet first\n• Get free test ETH from faucets\n• Never share private keys"
                android:textColor="#666666"
                android:textSize="11sp"
                android:lineSpacingExtra="2dp" />

        </LinearLayout>

    </LinearLayout>
</ScrollView>