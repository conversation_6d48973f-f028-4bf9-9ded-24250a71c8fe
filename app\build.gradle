apply plugin: 'com.android.application'

android {
    compileSdk 35

    defaultConfig {
        applicationId "com.gamegards.gaming27"
        minSdkVersion 23
        targetSdk 35
        versionCode 1
        versionName "1.2"
        multiDexEnabled true

        archivesBaseName = "gaming27games_" + versionCode
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    signingConfigs {
        debug {
            storeFile file("debug.keystore")
            storePassword "android"
            keyAlias "androiddebugkey"
            keyPassword "android"
        }
        release {
            storeFile file("C:\\Users\\<USER>\\Desktop\\akash\\AndroidStudioProjects\\play24\\secureplay999.jks")
            storePassword "123456"
            keyAlias "key9"
            keyPassword "123456"
        }
    }

    buildTypes {
        debug {
            signingConfig signingConfigs.release
        }
        release {
            minifyEnabled false
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
        }
    }

    repositories {
        jcenter()
        maven { url "https://jitpack.io" }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }

    packagingOptions {
        exclude 'META-INF/DEPENDENCIES'
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/NOTICE'
        exclude 'META-INF/NOTICE.txt'
    }

    lintOptions {
        checkReleaseBuilds false
        abortOnError false
    }

    namespace 'com.gamegards.gaming27'
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])

    implementation 'com.google.android.material:material:1.12.0'
    implementation 'androidx.appcompat:appcompat:1.7.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.2.0'
    implementation 'androidx.activity:activity:1.9.3'
    implementation 'androidx.work:work-runtime:2.10.0'

    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.2.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.6.1'

    implementation project(path: ':ludogame')

    // Web3 & WalletConnect
    implementation 'org.web3j:core:4.8.7'
    implementation 'org.web3j:crypto:4.8.7'
    implementation 'org.web3j:contracts:4.8.7'
    implementation 'org.bouncycastle:bcprov-jdk15on:1.68'

    implementation 'com.walletconnect:android-core:1.8.0'
    implementation('com.walletconnect:sign:2.0.0') {
        exclude group: 'com.walletconnect', module: 'android-core-impl'
    }

    // Kotlin coroutines
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4'

    // Image loading
    def glide_version = "4.13.2"
    implementation "com.github.bumptech.glide:glide:$glide_version"
    annotationProcessor "com.github.bumptech.glide:compiler:$glide_version"
    implementation 'com.squareup.picasso:picasso:2.71828'

    // Social logins
    implementation 'com.facebook.android:facebook-login:[8,9)'
    implementation 'com.google.android.gms:play-services-auth:21.3.0'

    // Payments
    implementation 'com.razorpay:checkout:1.6.41'
    implementation 'com.cashfree.pg:android-sdk:1.7.27'
    implementation 'com.paytm.appinvokesdk:appinvokesdk:1.6.8'
    implementation 'in.payu:payu-checkout-pro:2.4.5'

    // UI and tools
    implementation 'com.github.TecOrb-Developers:SmartAlertDialog:v1.0'
    implementation 'de.hdodenhof:circleimageview:3.1.0'
    implementation 'com.nhaarman.supertooltips:library:3.0.+'
    implementation 'com.eftimoff:android-pathview:1.0.8@aar'
    implementation 'pl.droidsonroids.gif:android-gif-drawable:1.2.29'
    implementation 'fr.avianey.com.viewpagerindicator:library:*******@aar'
    implementation('cn.trinea.android.view.autoscrollviewpager:android-auto-scroll-view-pager:1.1.2') {
        exclude module: 'support-v4'
    }

    // Networking
    implementation 'com.android.volley:volley:1.2.1'
    implementation 'io.reactivex.rxjava2:rxandroid:2.1.1'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.12.0'
    implementation 'com.google.code.gson:gson:2.11.0'

    def retrofitVersion = "2.9.0"
    def gsonConverterVersion = "2.9.0"
    def okhttpVersion = "4.12.0"
    implementation "com.squareup.retrofit2:retrofit:$retrofitVersion"
    implementation "com.squareup.retrofit2:converter-gson:$gsonConverterVersion"
    implementation "com.squareup.okhttp3:okhttp:$okhttpVersion"

    implementation 'com.jakewharton:butterknife:10.2.3'
    annotationProcessor 'com.jakewharton:butterknife-compiler:10.2.3'

    // UPI Payment
    implementation 'dev.shreyaspatil.EasyUpiPayment:EasyUpiPayment:3.0.3'

    // Socket
    implementation('io.socket:socket.io-client:2.1.1') {
        exclude group: 'org.json', module: 'json'
    }

    // Google location APIs
    implementation 'com.google.android.gms:play-services-location:21.3.0'
    implementation 'com.google.android.gms:play-services-places:17.1.0'
}

// If needed, uncomment Firebase plugins
// apply plugin: 'com.google.firebase.crashlytics'
// apply plugin: 'com.google.gms.google-services'

// Optional resolution strategy (if needed)
configurations.all {
    resolutionStrategy.eachDependency { DependencyResolveDetails details ->
        if (details.requested.group == 'com.walletconnect' && details.requested.name == 'android-core-impl') {
            details.useVersion '1.8.0'
        }
    }
}
