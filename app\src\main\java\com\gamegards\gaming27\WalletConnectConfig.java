package com.gamegards.gaming27;

/**
 * Configuration class for WalletConnect integration
 */
public class WalletConnectConfig {
    
    // WalletConnect Project ID - Replace with your actual project ID from WalletConnect Cloud
    // Get one from: https://cloud.walletconnect.com/
    public static final String PROJECT_ID = "b0cebcda95846f0aabc833a9f05dca99";
    
    // App metadata
    public static final String APP_NAME = "Gaming27 Web3";
    public static final String APP_DESCRIPTION = "Gaming27 Web3 Integration";
    public static final String APP_URL = "https://gaming27.com";
    public static final String APP_ICON = "https://gaming27.com/icon.png";
    
    // Supported chains
    public static final String ETHEREUM_MAINNET = "eip155:1";
    public static final String SEPOLIA_TESTNET = "eip155:11155111";
    public static final String POLYGON_MAINNET = "eip155:137";
    public static final String BSC_MAINNET = "eip155:56";
    
    // RPC URLs
    public static final String ETHEREUM_RPC = "https://mainnet.infura.io/v3/";
    public static final String SEPOLIA_RPC = "https://sepolia.infura.io/v3/";
    public static final String POLYGON_RPC = "https://polygon-rpc.com/";
    public static final String BSC_RPC = "https://bsc-dataseed.binance.org/";
    
    // Wallet package names
    public static final String TRUST_WALLET_PACKAGE = "com.wallet.crypto.trustapp";
    public static final String METAMASK_PACKAGE = "io.metamask";
    
    // Deep link schemes
    public static final String TRUST_WALLET_SCHEME = "trust://wc?uri=";
    public static final String METAMASK_SCHEME = "metamask://wc?uri=";
    
    // Play Store URLs
    public static final String TRUST_WALLET_PLAY_STORE = "https://play.google.com/store/apps/details?id=com.wallet.crypto.trustapp";
    public static final String METAMASK_PLAY_STORE = "https://play.google.com/store/apps/details?id=io.metamask";
    
    // Supported methods
    public static final String[] SUPPORTED_METHODS = {
        "eth_sendTransaction",
        "eth_signTransaction", 
        "eth_sign",
        "personal_sign",
        "eth_signTypedData",
        "eth_signTypedData_v4",
        "wallet_switchEthereumChain",
        "wallet_addEthereumChain",
        "eth_getBalance",
        "eth_getTransactionCount",
        "eth_gasPrice"
    };
    
    // Supported events
    public static final String[] SUPPORTED_EVENTS = {
        "chainChanged",
        "accountsChanged",
        "disconnect"
    };
}
