// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    
    repositories {
        google()
        jcenter()
        mavenCentral()

    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.8.1'
        // classpath 'com.google.gms:google-services:4.4.2'

        // classpath 'com.google.firebase:firebase-crashlytics-gradle:3.0.2'
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

allprojects {
    repositories {
        google()
        jcenter()
        mavenCentral()

        maven { url "https://jitpack.io" }
        maven { url "https://artifactory.paytm.in/libs-release-local" }
        maven {url 'https://maven.cashfree.com/release'}
        maven { url "https://maven.walletconnect.com/artifactory/gradle-release-local" }

    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}

ext {
    retrofitVersion = '2.6.2'
    gsonConverterVersion = '2.6.2'
    okhttpVersion = '4.2.2'
}

