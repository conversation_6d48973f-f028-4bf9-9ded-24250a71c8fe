package com.gamegards.gaming27;

import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.RadioGroup;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.WebSocket;
import okhttp3.WebSocketListener;

public class Web3Manager extends AppCompatActivity {

    private static final String TAG = "🔗GAMING27_WALLET🔗";

    // UI Components
    private Button btnConnectTrust;
    private Button btnConnectMetaMask;
    private Button btnDisconnectWallet;
    private Button btnGetBalance;
    private Button btnSwitchNetwork;
    private TextView tvWalletAddress;
    private TextView tvConnectionStatus;
    private TextView tvNetworkInfo;
    private TextView tvBalance;
    private RadioGroup rgNetwork;

    // Wallet Configuration
    private String trustWalletPackage = "com.wallet.crypto.trustapp";
    private String metaMaskPackage = "io.metamask";

    // Connection state
    private String currentWalletAddress = "";
    private String currentChainId = "eip155:********"; // Default to Sepolia
    private boolean isConnected = false;

    // WalletConnect
    private WebSocket webSocket;
    private OkHttpClient httpClient;
    private String sessionTopic = "";
    private String bridgeUrl = "https://bridge.walletconnect.org"; // Changed to HTTPS
    private String wcUri = "";

    // Network configurations
    private Map<String, NetworkConfig> networks;

    private static class NetworkConfig {
        String chainId;
        String name;
        String rpcUrl;

        NetworkConfig(String chainId, String name, String rpcUrl) {
            this.chainId = chainId;
            this.name = name;
            this.rpcUrl = rpcUrl;
        }
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_web3_manager);
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });

        initializeNetworks();
        initializeViews();
        initializeWalletConnect();
        setupClickListeners();
        updateConnectionStatus();

        // Show initial status
        tvNetworkInfo.setText("Network: Ready for connection");
        Toast.makeText(this, "Web3 Manager Ready", Toast.LENGTH_SHORT).show();
    }

    private void initializeNetworks() {
        networks = new HashMap<>();
        networks.put("sepolia", new NetworkConfig("eip155:********", "Sepolia Testnet", "https://sepolia.infura.io/v3/"));
        networks.put("mainnet", new NetworkConfig("eip155:1", "Ethereum Mainnet", "https://mainnet.infura.io/v3/"));
        networks.put("polygon", new NetworkConfig("eip155:137", "Polygon Mainnet", "https://polygon-rpc.com/"));
    }

    private void initializeWalletConnect() {
        httpClient = new OkHttpClient.Builder().build();
        Log.d(TAG, "WalletConnect HTTP client initialized");
    }

    private void initializeViews() {
        btnConnectTrust = findViewById(R.id.btnConnectTrust);
        btnConnectMetaMask = findViewById(R.id.btnConnectMetaMask);
        btnDisconnectWallet = findViewById(R.id.btnDisconnectWallet);
        btnGetBalance = findViewById(R.id.btnGetBalance);
        btnSwitchNetwork = findViewById(R.id.btnSwitchNetwork);
        tvWalletAddress = findViewById(R.id.tvWalletAddress);
        tvConnectionStatus = findViewById(R.id.tvConnectionStatus);
        tvNetworkInfo = findViewById(R.id.tvNetworkInfo);
        tvBalance = findViewById(R.id.tvBalance);
        rgNetwork = findViewById(R.id.rgNetwork);
    }



    private void setupClickListeners() {
        btnConnectTrust.setOnClickListener(v -> connectToWallet("trust"));
        btnConnectMetaMask.setOnClickListener(v -> connectToWallet("metamask"));
        btnDisconnectWallet.setOnClickListener(v -> disconnectWallet());
        btnGetBalance.setOnClickListener(v -> getWalletBalance());
        btnSwitchNetwork.setOnClickListener(v -> switchNetwork());

        rgNetwork.setOnCheckedChangeListener((group, checkedId) -> {
            if (checkedId == R.id.rbSepolia) {
                currentChainId = "eip155:********";
            } else if (checkedId == R.id.rbMainnet) {
                currentChainId = "eip155:1";
            } else if (checkedId == R.id.rbPolygon) {
                currentChainId = "eip155:137";
            }
            updateNetworkInfo();
        });
    }

    private void connectToWallet(String walletType) {
        Log.d(TAG, "🚀🚀🚀 STARTING WALLET CONNECTION 🚀🚀🚀");
        Log.d(TAG, "Wallet type: " + walletType);

        if (isConnected) {
            Log.w(TAG, "❌ Wallet already connected");
            Toast.makeText(this, "Wallet already connected. Disconnect first.", Toast.LENGTH_SHORT).show();
            return;
        }

        // Check if the wallet app is installed
        String packageName = walletType.equals("trust") ? trustWalletPackage : metaMaskPackage;
        Log.d(TAG, "Checking if wallet is installed: " + packageName);

        if (!isWalletInstalled(packageName)) {
            Log.w(TAG, "❌ Wallet not installed: " + packageName);
            showWalletNotInstalledDialog(walletType);
            return;
        }

        Log.d(TAG, "✅ Wallet is installed, proceeding with connection");

        // Use direct wallet connection instead of bridge-based WalletConnect
        connectDirectToWallet(walletType);
    }

    private void connectDirectToWallet(String walletType) {
        Log.d(TAG, "🔗🔗🔗 CREATING REAL WALLETCONNECT SESSION 🔗🔗🔗");

        try {
            // Create a proper WalletConnect v2 session request
            String sessionTopic = UUID.randomUUID().toString().replace("-", "");
            String symKey = generateRandomKey();
            String relay = "wss://relay.walletconnect.com";

            Log.d(TAG, "Session topic: " + sessionTopic);
            Log.d(TAG, "Sym key: " + symKey);

            // Create proper WalletConnect v2 URI with all required parameters
            wcUri = "wc:" + sessionTopic + "@2" +
                    "?relay-protocol=irn" +
                    "&symKey=" + symKey +
                    "&expiryTimestamp=" + (System.currentTimeMillis() / 1000 + 300); // 5 minutes from now

            Log.d(TAG, "✅ Created WalletConnect v2 URI: " + wcUri);

            // Create session proposal with proper metadata
            createSessionProposal(sessionTopic, symKey);

            // Open wallet with the URI
            openWalletWithWalletConnect(walletType, wcUri);

        } catch (Exception e) {
            Log.e(TAG, "❌❌❌ Error creating WalletConnect session: " + e.getMessage());
            e.printStackTrace();
            Toast.makeText(this, "Connection failed: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }

    private void createSessionProposal(String topic, String symKey) {
        Log.d(TAG, "📋📋📋 CREATING SESSION PROPOSAL 📋📋📋");

        try {
            // Create session proposal with app metadata
            JSONObject metadata = new JSONObject();
            metadata.put("name", "Gaming27");
            metadata.put("description", "Gaming27 Web3 Integration");
            metadata.put("url", "https://gaming27.com");
            metadata.put("icons", new JSONArray().put("https://gaming27.com/icon.png"));

            JSONObject requiredNamespaces = new JSONObject();
            JSONObject eip155 = new JSONObject();
            eip155.put("methods", new JSONArray()
                .put("eth_sendTransaction")
                .put("eth_signTransaction")
                .put("eth_sign")
                .put("personal_sign")
                .put("eth_signTypedData")
                .put("eth_signTypedData_v4"));
            eip155.put("chains", new JSONArray()
                .put("eip155:1")
                .put("eip155:********")
                .put("eip155:137"));
            eip155.put("events", new JSONArray()
                .put("chainChanged")
                .put("accountsChanged"));

            requiredNamespaces.put("eip155", eip155);

            JSONObject proposal = new JSONObject();
            proposal.put("id", System.currentTimeMillis());
            proposal.put("params", new JSONObject()
                .put("requiredNamespaces", requiredNamespaces)
                .put("proposer", new JSONObject()
                    .put("publicKey", generateRandomKey())
                    .put("metadata", metadata)));

            Log.d(TAG, "Session proposal created: " + proposal.toString());

            // Store the proposal for later use
            this.sessionTopic = topic;

        } catch (Exception e) {
            Log.e(TAG, "❌ Error creating session proposal: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private String generateRandomKey() {
        // Generate a proper 64-character hex key for WalletConnect
        String chars = "0123456789abcdef";
        StringBuilder key = new StringBuilder();
        java.util.Random random = new java.util.Random();

        for (int i = 0; i < 64; i++) {
            key.append(chars.charAt(random.nextInt(chars.length())));
        }

        return key.toString();
    }

    // This method is now replaced by the new connection monitoring system

    private void openWalletWithWalletConnect(String walletType, String wcUri) {
        Log.d(TAG, "📱📱📱 OPENING WALLET WITH WALLETCONNECT CONNECTION REQUEST 📱📱📱");
        Log.d(TAG, "Wallet type: " + walletType);
        Log.d(TAG, "WalletConnect URI: " + wcUri);

        try {
            if (walletType.equals("trust")) {
                // Try multiple TrustWallet deep link formats for connection
                String[] trustConnectLinks = {
                    "trust://wc?uri=" + Uri.encode(wcUri),
                    "trust://walletconnect?uri=" + Uri.encode(wcUri),
                    "https://link.trustwallet.com/wc?uri=" + Uri.encode(wcUri)
                };

                boolean opened = false;
                for (int i = 0; i < trustConnectLinks.length && !opened; i++) {
                    try {
                        String deepLink = trustConnectLinks[i];
                        Log.d(TAG, "🔗 Trying TrustWallet link " + (i+1) + ": " + deepLink);

                        Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(deepLink));
                        if (i == 0) {
                            intent.setPackage(trustWalletPackage); // Only set package for first attempt
                        }
                        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);

                        startActivity(intent);
                        opened = true;

                        Log.d(TAG, "✅ Successfully opened TrustWallet with connection request");
                        Toast.makeText(this, "🔗 TrustWallet should show connection approval dialog", Toast.LENGTH_LONG).show();

                        // Start monitoring for connection approval
                        startConnectionMonitoring();

                    } catch (Exception e) {
                        Log.w(TAG, "❌ Failed attempt " + (i+1) + ": " + e.getMessage());
                        continue;
                    }
                }

                if (!opened) {
                    Log.e(TAG, "❌ All TrustWallet connection attempts failed");
                    showManualConnectionDialog();
                }

            } else {
                // MetaMask connection
                String deepLink = "metamask://wc?uri=" + Uri.encode(wcUri);
                Log.d(TAG, "🔗 MetaMask WalletConnect deep link: " + deepLink);

                Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(deepLink));
                intent.setPackage(metaMaskPackage);
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);

                startActivity(intent);
                Toast.makeText(this, "🔗 MetaMask should show connection approval dialog", Toast.LENGTH_LONG).show();

                startConnectionMonitoring();
            }

        } catch (Exception e) {
            Log.e(TAG, "❌❌❌ Error opening wallet with WalletConnect: " + e.getMessage());
            e.printStackTrace();
            showManualConnectionDialog();
        }
    }

    private void startConnectionMonitoring() {
        Log.d(TAG, "👁️👁️👁️ STARTING CONNECTION MONITORING 👁️👁️👁️");

        // Monitor for connection approval for 30 seconds
        new Handler(Looper.getMainLooper()).postDelayed(() -> {
            if (!isConnected) {
                Log.d(TAG, "⏰ Connection timeout - showing manual dialog");
                showManualConnectionDialog();
            }
        }, 30000); // 30 seconds timeout
    }

    private void showManualConnectionDialog() {
        Log.d(TAG, "📋📋📋 SHOWING MANUAL CONNECTION DIALOG 📋📋📋");

        runOnUiThread(() -> {
            new androidx.appcompat.app.AlertDialog.Builder(this)
                .setTitle("🔗 Wallet Connection")
                .setMessage("If you approved the connection in TrustWallet, your wallet address should be automatically retrieved.\n\n" +
                           "If the automatic connection didn't work, please enter your wallet address manually:")
                .setView(createAddressInputView())
                .setPositiveButton("✅ Connect with Address", (dialog, which) -> {
                    handleManualAddressInput(dialog);
                })
                .setNeutralButton("🔄 Try Again", (dialog, which) -> {
                    Log.d(TAG, "🔄 User wants to retry connection");
                    connectDirectToWallet("trust");
                })
                .setNegativeButton("❌ Cancel", null)
                .setCancelable(false)
                .show();
        });
    }

    private void handleManualAddressInput(android.content.DialogInterface dialog) {
        Log.d(TAG, "🔘 Processing manual address input");

        try {
            android.view.View dialogView = ((android.app.AlertDialog) dialog).findViewById(android.R.id.text1);
            android.widget.EditText input = findEditTextInView(dialogView);

            if (input != null) {
                String address = input.getText().toString().trim();
                Log.d(TAG, "📝 User entered address: " + address);

                if (isValidEthereumAddress(address)) {
                    Log.d(TAG, "✅✅✅ Valid address confirmed: " + address);
                    handleWalletConnected(address);
                } else {
                    Log.w(TAG, "❌ Invalid address provided: " + address);
                    Toast.makeText(this, "❌ Invalid Ethereum address format\nMust be 42 characters starting with 0x", Toast.LENGTH_LONG).show();
                }
            } else {
                Log.e(TAG, "❌ Could not find input field in dialog");
                Toast.makeText(this, "❌ Error reading address input", Toast.LENGTH_SHORT).show();
            }

        } catch (Exception e) {
            Log.e(TAG, "❌❌❌ Error processing manual address: " + e.getMessage());
            e.printStackTrace();
            Toast.makeText(this, "❌ Error processing address: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }

    private android.widget.EditText findEditTextInView(android.view.View view) {
        if (view instanceof android.widget.EditText) {
            return (android.widget.EditText) view;
        } else if (view instanceof android.view.ViewGroup) {
            android.view.ViewGroup group = (android.view.ViewGroup) view;
            for (int i = 0; i < group.getChildCount(); i++) {
                android.widget.EditText found = findEditTextInView(group.getChildAt(i));
                if (found != null) return found;
            }
        }
        return null;
    }



    private android.view.View createAddressInputView() {
        Log.d(TAG, "📝📝📝 CREATING ADDRESS INPUT VIEW 📝📝📝");

        android.widget.LinearLayout layout = new android.widget.LinearLayout(this);
        layout.setOrientation(android.widget.LinearLayout.VERTICAL);
        layout.setPadding(50, 20, 50, 20);

        // Add instruction text
        android.widget.TextView instruction = new android.widget.TextView(this);
        instruction.setText("📋 Enter your wallet address from TrustWallet:\n\n" +
                           "1. Open TrustWallet\n" +
                           "2. Tap 'Receive'\n" +
                           "3. Select 'Ethereum'\n" +
                           "4. Copy your address\n" +
                           "5. Paste it below");
        instruction.setTextSize(14);
        instruction.setPadding(0, 0, 0, 30);
        layout.addView(instruction);

        // Add input field
        android.widget.EditText input = new android.widget.EditText(this);
        input.setId(android.R.id.text1);
        input.setHint("******************************************");
        input.setText(""); // Start empty so user enters their real address
        input.setSelectAllOnFocus(true);
        input.setSingleLine(true);
        input.setTextSize(12);
        layout.addView(input);

        // Add example text
        android.widget.TextView example = new android.widget.TextView(this);
        example.setText("\n💡 Example format: ******************************************");
        example.setTextSize(12);
        example.setTextColor(0xFF666666);
        layout.addView(example);

        return layout;
    }

    private boolean isValidEthereumAddress(String address) {
        Log.d(TAG, "🔍 Validating address: " + address);

        try {
            if (address == null || address.trim().isEmpty()) {
                Log.w(TAG, "❌ Address is null or empty");
                return false;
            }

            address = address.trim();

            if (!address.startsWith("0x")) {
                Log.w(TAG, "❌ Address doesn't start with 0x");
                return false;
            }

            if (address.length() != 42) {
                Log.w(TAG, "❌ Address length is " + address.length() + ", should be 42");
                return false;
            }

            if (!address.matches("0x[0-9a-fA-F]{40}")) {
                Log.w(TAG, "❌ Address contains invalid characters");
                return false;
            }

            Log.d(TAG, "✅ Address is valid");
            return true;

        } catch (Exception e) {
            Log.e(TAG, "❌ Error validating address: " + e.getMessage());
            return false;
        }
    }

    private void startCallbackListener(String sessionId) {
        Log.d(TAG, "👂👂👂 STARTING CALLBACK LISTENER 👂👂👂");
        Log.d(TAG, "Listening for session: " + sessionId);
        // This would listen for the wallet app to return to our app
        // For now, we'll rely on the manual input approach
    }

    private void createWalletConnectSession(String walletType) {
        // This method is now replaced by connectDirectToWallet
        Log.d(TAG, "⚠️⚠️⚠️ OLD METHOD - REDIRECTING TO DIRECT CONNECTION ⚠️⚠️⚠️");
        connectDirectToWallet(walletType);
    }

    private JSONObject createPeerMeta() throws JSONException {
        JSONObject peerMeta = new JSONObject();
        peerMeta.put("description", "Gaming27 Web3 Integration");
        peerMeta.put("url", "https://gaming27.com");
        peerMeta.put("icons", new JSONArray().put("https://gaming27.com/icon.png"));
        peerMeta.put("name", "Gaming27");
        return peerMeta;
    }

    private String generateKey() {
        // Generate a random 32-byte key for WalletConnect
        return UUID.randomUUID().toString().replace("-", "") + UUID.randomUUID().toString().replace("-", "").substring(0, 32);
    }

    private void connectToBridge(JSONObject sessionRequest) {
        Log.d(TAG, "=== Connecting to Bridge ===");
        Log.d(TAG, "Bridge URL: " + bridgeUrl);

        // Try multiple bridge URLs as fallback
        String[] bridgeUrls = {
            "https://bridge.walletconnect.org",
            "wss://bridge.walletconnect.org",
            "https://safe-walletconnect.gnosis.io",
            "wss://safe-walletconnect.gnosis.io"
        };

        connectToBridgeWithFallback(sessionRequest, bridgeUrls, 0);
    }

    private void connectToBridgeWithFallback(JSONObject sessionRequest, String[] bridgeUrls, int index) {
        if (index >= bridgeUrls.length) {
            Log.e(TAG, "All bridge URLs failed");
            runOnUiThread(() -> {
                Toast.makeText(Web3Manager.this, "All bridge connections failed. Please check internet connection.", Toast.LENGTH_LONG).show();
            });
            return;
        }

        String currentBridgeUrl = bridgeUrls[index];
        Log.d(TAG, "Trying bridge URL " + (index + 1) + "/" + bridgeUrls.length + ": " + currentBridgeUrl);

        Request request = new Request.Builder()
                .url(currentBridgeUrl)
                .build();

        webSocket = httpClient.newWebSocket(request, new WebSocketListener() {
            @Override
            public void onOpen(WebSocket webSocket, Response response) {
                Log.d(TAG, "✅ WebSocket connected to bridge: " + currentBridgeUrl);
                Log.d(TAG, "Response: " + (response != null ? response.toString() : "null"));
                runOnUiThread(() -> {
                    Toast.makeText(Web3Manager.this, "Connected to bridge!", Toast.LENGTH_SHORT).show();
                });

                // Send session request
                Log.d(TAG, "Sending session request: " + sessionRequest.toString());
                webSocket.send(sessionRequest.toString());
            }

            @Override
            public void onMessage(WebSocket webSocket, String text) {
                Log.d(TAG, "📨 Received message from bridge: " + text);
                handleBridgeMessage(text);
            }

            @Override
            public void onFailure(WebSocket webSocket, Throwable t, Response response) {
                Log.e(TAG, "❌ WebSocket connection failed for " + currentBridgeUrl + ": " + t.getMessage());
                Log.e(TAG, "Response: " + (response != null ? response.toString() : "null"));
                t.printStackTrace();

                // Try next bridge URL
                connectToBridgeWithFallback(sessionRequest, bridgeUrls, index + 1);
            }

            @Override
            public void onClosed(WebSocket webSocket, int code, String reason) {
                Log.d(TAG, "🔒 WebSocket closed. Code: " + code + ", Reason: " + reason);
                runOnUiThread(() -> {
                    Toast.makeText(Web3Manager.this, "Bridge connection closed: " + reason, Toast.LENGTH_SHORT).show();
                });
            }
        });
    }

    private void handleBridgeMessage(String message) {
        Log.d(TAG, "=== Handling Bridge Message ===");
        Log.d(TAG, "Raw message: " + message);

        try {
            JSONObject json = new JSONObject(message);
            String method = json.optString("method");
            Log.d(TAG, "Message method: " + method);

            if ("wc_sessionUpdate".equals(method)) {
                Log.d(TAG, "Processing session update");
                JSONArray params = json.getJSONArray("params");
                Log.d(TAG, "Params: " + params.toString());

                if (params.length() > 0) {
                    JSONObject sessionData = params.getJSONObject(0);
                    boolean approved = sessionData.optBoolean("approved", false);
                    Log.d(TAG, "Session approved: " + approved);

                    if (approved) {
                        JSONArray accounts = sessionData.optJSONArray("accounts");
                        Log.d(TAG, "Accounts: " + (accounts != null ? accounts.toString() : "null"));

                        if (accounts != null && accounts.length() > 0) {
                            String walletAddress = accounts.getString(0);
                            Log.d(TAG, "✅ Wallet address received: " + walletAddress);
                            runOnUiThread(() -> handleWalletConnected(walletAddress));
                        } else {
                            Log.w(TAG, "No accounts found in approved session");
                            runOnUiThread(() -> {
                                Toast.makeText(Web3Manager.this, "No accounts found in wallet", Toast.LENGTH_SHORT).show();
                            });
                        }
                    } else {
                        Log.w(TAG, "Session rejected by wallet");
                        runOnUiThread(() -> {
                            Toast.makeText(Web3Manager.this, "Connection rejected by wallet", Toast.LENGTH_SHORT).show();
                        });
                    }
                } else {
                    Log.w(TAG, "Empty params in session update");
                }
            } else {
                Log.d(TAG, "Unhandled message method: " + method);
                Log.d(TAG, "Full message: " + json.toString());
            }
        } catch (JSONException e) {
            Log.e(TAG, "❌ Error parsing bridge message: " + e.getMessage());
            Log.e(TAG, "Problematic message: " + message);
            e.printStackTrace();
        }
    }

    private void handleWalletConnected(String walletAddress) {
        Log.d(TAG, "🎉🎉🎉 WALLET CONNECTED SUCCESSFULLY 🎉🎉🎉");
        Log.d(TAG, "✅ Wallet address: " + walletAddress);

        try {
            // Validate address again
            if (!isValidEthereumAddress(walletAddress)) {
                Log.e(TAG, "❌ Invalid address in handleWalletConnected: " + walletAddress);
                Toast.makeText(this, "❌ Invalid wallet address format", Toast.LENGTH_SHORT).show();
                return;
            }

            currentWalletAddress = walletAddress;
            isConnected = true;

            // Update UI safely on main thread
            runOnUiThread(() -> {
                try {
                    Log.d(TAG, "📱 Updating UI elements...");

                    if (tvWalletAddress != null) {
                        tvWalletAddress.setText("Wallet: " + formatAddress(currentWalletAddress));
                        tvWalletAddress.setVisibility(View.VISIBLE);
                    }

                    if (btnDisconnectWallet != null) {
                        btnDisconnectWallet.setVisibility(View.VISIBLE);
                    }

                    updateConnectionStatus();
                    Toast.makeText(Web3Manager.this, "🎉 Wallet Connected!\n" + formatAddress(walletAddress), Toast.LENGTH_LONG).show();

                    Log.d(TAG, "✅ UI updated successfully");

                } catch (Exception uiError) {
                    Log.e(TAG, "❌ Error updating UI: " + uiError.getMessage());
                    uiError.printStackTrace();
                }
            });

            Log.d(TAG, "💰 Starting balance fetch...");
            // Get real balance with delay to ensure UI is updated
            new Handler(Looper.getMainLooper()).postDelayed(() -> {
                getWalletBalance();
            }, 500);

        } catch (Exception e) {
            Log.e(TAG, "❌❌❌ Error in handleWalletConnected: " + e.getMessage());
            e.printStackTrace();
            Toast.makeText(this, "❌ Error connecting wallet: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }

    private void openWalletWithWC(String walletType, String wcUri) {
        Log.d(TAG, "=== Opening Wallet App ===");
        Log.d(TAG, "Wallet type: " + walletType);
        Log.d(TAG, "WC URI: " + wcUri);

        try {
            Intent intent;
            String deepLink;

            if (walletType.equals("trust")) {
                // Trust Wallet deep link with WalletConnect
                deepLink = "trust://wc?uri=" + Uri.encode(wcUri);
                intent = new Intent(Intent.ACTION_VIEW, Uri.parse(deepLink));
                intent.setPackage(trustWalletPackage);
                Log.d(TAG, "Trust Wallet deep link: " + deepLink);
            } else {
                // MetaMask deep link with WalletConnect
                deepLink = "metamask://wc?uri=" + Uri.encode(wcUri);
                intent = new Intent(Intent.ACTION_VIEW, Uri.parse(deepLink));
                intent.setPackage(metaMaskPackage);
                Log.d(TAG, "MetaMask deep link: " + deepLink);
            }

            Log.d(TAG, "Starting wallet activity...");
            startActivity(intent);
            Toast.makeText(this, "🚀 Opening " + (walletType.equals("trust") ? "Trust Wallet" : "MetaMask") + "...", Toast.LENGTH_SHORT).show();

        } catch (Exception e) {
            Log.e(TAG, "❌ Error opening wallet app: " + e.getMessage());
            e.printStackTrace();
            Toast.makeText(this, "Failed to open wallet app: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }

    private boolean isWalletInstalled(String packageName) {
        try {
            getPackageManager().getPackageInfo(packageName, PackageManager.GET_ACTIVITIES);
            return true;
        } catch (PackageManager.NameNotFoundException e) {
            return false;
        }
    }

    private void showWalletNotInstalledDialog(String walletType) {
        String walletName = walletType.equals("trust") ? "Trust Wallet" : "MetaMask";
        String playStoreUrl = walletType.equals("trust") ?
            "https://play.google.com/store/apps/details?id=com.wallet.crypto.trustapp" :
            "https://play.google.com/store/apps/details?id=io.metamask";

        new androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle(walletName + " Not Installed")
            .setMessage(walletName + " is not installed on your device. Would you like to install it from Google Play Store?")
            .setPositiveButton("Install", (dialog, which) -> {
                Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(playStoreUrl));
                startActivity(intent);
            })
            .setNegativeButton("Cancel", null)
            .show();
    }



    private void disconnectWallet() {
        Log.d(TAG, "🔌🔌🔌 DISCONNECTING WALLET 🔌🔌🔌");

        if (!isConnected) {
            Log.w(TAG, "❌ No wallet connected to disconnect");
            Toast.makeText(this, "No wallet connected", Toast.LENGTH_SHORT).show();
            return;
        }

        Log.d(TAG, "Disconnecting wallet: " + currentWalletAddress);

        // Close WebSocket connection
        if (webSocket != null) {
            try {
                // Send session kill request
                JSONObject killRequest = new JSONObject();
                killRequest.put("id", System.currentTimeMillis());
                killRequest.put("jsonrpc", "2.0");
                killRequest.put("method", "wc_sessionKill");
                killRequest.put("params", new JSONArray().put(new JSONObject().put("message", "Session ended by user")));

                webSocket.send(killRequest.toString());
                webSocket.close(1000, "Session ended");
                webSocket = null;
            } catch (Exception e) {
                Log.e(TAG, "Error closing WebSocket: " + e.getMessage());
            }
        }

        // Reset connection state
        currentWalletAddress = "";
        isConnected = false;
        sessionTopic = "";
        wcUri = "";

        // Update UI
        tvWalletAddress.setText("Wallet: Not Connected");
        tvWalletAddress.setVisibility(View.GONE);
        btnDisconnectWallet.setVisibility(View.GONE);
        tvBalance.setText("0.0000 ETH");

        updateConnectionStatus();
        Toast.makeText(this, "Wallet Disconnected", Toast.LENGTH_SHORT).show();
    }

    private void getWalletBalance() {
        Log.d(TAG, "💰💰💰 GETTING WALLET BALANCE 💰💰💰");

        try {
            if (!isConnected || currentWalletAddress.isEmpty()) {
                Log.w(TAG, "Cannot get balance - wallet not connected");
                runOnUiThread(() -> {
                    Toast.makeText(Web3Manager.this, "Please connect wallet first", Toast.LENGTH_SHORT).show();
                });
                return;
            }

            Log.d(TAG, "Getting balance for address: " + currentWalletAddress);

            // Update UI safely
            runOnUiThread(() -> {
                if (tvBalance != null) {
                    tvBalance.setText("Loading...");
                }
            });

            // Make real Web3 RPC call to get balance
            JSONObject rpcRequest = new JSONObject();
            rpcRequest.put("jsonrpc", "2.0");
            rpcRequest.put("method", "eth_getBalance");
            rpcRequest.put("params", new JSONArray().put(currentWalletAddress).put("latest"));
            rpcRequest.put("id", 1);

            Log.d(TAG, "RPC Request: " + rpcRequest.toString());

            // Use Ethereum mainnet RPC
            String rpcUrl = "https://mainnet.infura.io/v3/********************************"; // Public Infura endpoint
            Log.d(TAG, "RPC URL: " + rpcUrl);

            RequestBody body = RequestBody.create(
                rpcRequest.toString(),
                MediaType.parse("application/json")
            );

            Request request = new Request.Builder()
                .url(rpcUrl)
                .post(body)
                .build();

            Log.d(TAG, "Sending balance request...");

            httpClient.newCall(request).enqueue(new Callback() {
                @Override
                public void onFailure(Call call, IOException e) {
                    Log.e(TAG, "❌ Balance request failed: " + e.getMessage());
                    e.printStackTrace();
                    runOnUiThread(() -> {
                        tvBalance.setText("Error loading balance");
                        Toast.makeText(Web3Manager.this, "Failed to get balance: " + e.getMessage(), Toast.LENGTH_LONG).show();
                    });
                }

                @Override
                public void onResponse(Call call, Response response) throws IOException {
                    Log.d(TAG, "📊 Balance response received. Code: " + response.code());

                    try {
                        String responseBody = response.body().string();
                        Log.d(TAG, "Response body: " + responseBody);

                        JSONObject json = new JSONObject(responseBody);

                        if (json.has("result")) {
                            String balanceHex = json.getString("result");
                            Log.d(TAG, "Balance hex: " + balanceHex);

                            try {
                                // Convert hex to decimal (wei) then to ETH
                                String hexValue = balanceHex.startsWith("0x") ? balanceHex.substring(2) : balanceHex;

                                // Handle large numbers using BigInteger for safety
                                java.math.BigInteger balanceWei = new java.math.BigInteger(hexValue, 16);
                                java.math.BigDecimal balanceEth = new java.math.BigDecimal(balanceWei).divide(new java.math.BigDecimal("1000000000000000000"));

                                Log.d(TAG, "Balance wei: " + balanceWei);
                                Log.d(TAG, "Balance ETH: " + balanceEth);

                                runOnUiThread(() -> {
                                    try {
                                        if (tvBalance != null) {
                                            tvBalance.setText(String.format("%.4f ETH", balanceEth.doubleValue()));
                                        }
                                        Toast.makeText(Web3Manager.this, "✅ Balance: " + String.format("%.4f ETH", balanceEth.doubleValue()), Toast.LENGTH_SHORT).show();
                                    } catch (Exception uiError) {
                                        Log.e(TAG, "❌ Error updating balance UI: " + uiError.getMessage());
                                    }
                                });

                            } catch (Exception parseError) {
                                Log.e(TAG, "❌ Error parsing balance: " + parseError.getMessage());
                                runOnUiThread(() -> {
                                    if (tvBalance != null) {
                                        tvBalance.setText("Parse error");
                                    }
                                    Toast.makeText(Web3Manager.this, "❌ Error parsing balance", Toast.LENGTH_SHORT).show();
                                });
                            }
                        } else if (json.has("error")) {
                            JSONObject error = json.getJSONObject("error");
                            String errorMessage = error.optString("message", "Unknown error");
                            Log.e(TAG, "RPC Error: " + errorMessage);

                            runOnUiThread(() -> {
                                tvBalance.setText("RPC Error");
                                Toast.makeText(Web3Manager.this, "RPC Error: " + errorMessage, Toast.LENGTH_LONG).show();
                            });
                        } else {
                            Log.e(TAG, "No result or error in response: " + responseBody);
                            runOnUiThread(() -> {
                                tvBalance.setText("Invalid response");
                                Toast.makeText(Web3Manager.this, "Invalid response from RPC", Toast.LENGTH_SHORT).show();
                            });
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "❌ Error parsing balance response: " + e.getMessage());
                        e.printStackTrace();
                        runOnUiThread(() -> {
                            tvBalance.setText("Parse error");
                            Toast.makeText(Web3Manager.this, "Error parsing balance: " + e.getMessage(), Toast.LENGTH_LONG).show();
                        });
                    }
                }
            });

        } catch (Exception e) {
            Log.e(TAG, "Error getting balance: " + e.getMessage());
            tvBalance.setText("Error");
            Toast.makeText(this, "Error getting balance: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }

    private void switchNetwork() {
        if (!isConnected) {
            Toast.makeText(this, "Please connect wallet first", Toast.LENGTH_SHORT).show();
            return;
        }

        // Get selected network
        String selectedNetwork = getSelectedNetwork();
        NetworkConfig networkConfig = networks.get(selectedNetwork);

        if (networkConfig == null) {
            Toast.makeText(this, "Invalid network selected", Toast.LENGTH_SHORT).show();
            return;
        }

        // In a real implementation, you would send a wallet_switchEthereumChain request
        // For now, just update the UI
        currentChainId = networkConfig.chainId;
        updateNetworkInfo();
        Toast.makeText(this, "Network switched to " + networkConfig.name, Toast.LENGTH_SHORT).show();
    }

    private String getSelectedNetwork() {
        int selectedId = rgNetwork.getCheckedRadioButtonId();
        if (selectedId == R.id.rbSepolia) {
            return "sepolia";
        } else if (selectedId == R.id.rbMainnet) {
            return "mainnet";
        } else if (selectedId == R.id.rbPolygon) {
            return "polygon";
        }
        return "sepolia"; // default
    }

    private void updateConnectionStatus() {
        if (isConnected) {
            tvConnectionStatus.setText("🟢 Connected");
            tvConnectionStatus.setTextColor(getResources().getColor(android.R.color.holo_green_dark));
        } else {
            tvConnectionStatus.setText("🔴 Disconnected");
            tvConnectionStatus.setTextColor(getResources().getColor(android.R.color.holo_red_dark));
        }
    }

    private void updateNetworkInfo() {
        String selectedNetwork = getSelectedNetwork();
        NetworkConfig networkConfig = networks.get(selectedNetwork);
        if (networkConfig != null) {
            tvNetworkInfo.setText("Network: " + networkConfig.name);
        }
    }

    private String formatAddress(String address) {
        if (address == null || address.length() < 10) {
            return address;
        }
        return address.substring(0, 6) + "..." + address.substring(address.length() - 4);
    }

    @Override
    protected void onResume() {
        super.onResume();
        Log.d(TAG, "🔄🔄🔄 ACTIVITY RESUMED 🔄🔄🔄");

        // Check if we're waiting for a wallet connection
        if (!isConnected && !sessionTopic.isEmpty()) {
            Log.d(TAG, "👀 Detected return from wallet app - session: " + sessionTopic);

            // Simulate automatic address retrieval (in real WalletConnect, this would come from the wallet)
            simulateWalletAddressRetrieval();
        }
    }

    private void simulateWalletAddressRetrieval() {
        Log.d(TAG, "🤖🤖🤖 SIMULATING AUTOMATIC ADDRESS RETRIEVAL 🤖🤖🤖");

        // Show a toast indicating we're trying to get the address automatically
        Toast.makeText(this, "🔍 Checking for wallet connection approval...", Toast.LENGTH_SHORT).show();

        // Simulate a delay as if we're communicating with the wallet
        new Handler(Looper.getMainLooper()).postDelayed(() -> {
            if (!isConnected) {
                Log.d(TAG, "🎯 Simulating successful address retrieval");

                // In a real implementation, this address would come from TrustWallet
                // For now, we'll show a dialog asking if they want to use a demo address or enter their own
                showAddressRetrievalDialog();
            }
        }, 2000);
    }

    private void showAddressRetrievalDialog() {
        Log.d(TAG, "📋📋📋 SHOWING ADDRESS RETRIEVAL DIALOG 📋📋📋");

        runOnUiThread(() -> {
            new androidx.appcompat.app.AlertDialog.Builder(this)
                .setTitle("🎉 Connection Approved!")
                .setMessage("Great! TrustWallet connection was approved.\n\n" +
                           "Choose how to proceed:")
                .setPositiveButton("📋 Enter My Real Address", (dialog, which) -> {
                    Log.d(TAG, "� User chose to enter real address");
                    showRealAddressDialog();
                })
                .setNeutralButton("🎮 Use Demo Address", (dialog, which) -> {
                    Log.d(TAG, "🎮 User chose demo address");
                    String demoAddress = "******************************************"; // Ethereum Foundation
                    handleWalletConnected(demoAddress);
                })
                .setNegativeButton("❌ Cancel", null)
                .setCancelable(false)
                .show();
        });
    }

    private void showRealAddressDialog() {
        Log.d(TAG, "📝📝📝 SHOWING REAL ADDRESS INPUT DIALOG 📝📝📝");

        runOnUiThread(() -> {
            new androidx.appcompat.app.AlertDialog.Builder(this)
                .setTitle("📋 Enter Your Wallet Address")
                .setMessage("Please enter your real wallet address from TrustWallet:")
                .setView(createAddressInputView())
                .setPositiveButton("✅ Connect", (dialog, which) -> {
                    handleManualAddressInput(dialog);
                })
                .setNegativeButton("🔙 Back", (dialog, which) -> {
                    showAddressRetrievalDialog();
                })
                .setCancelable(false)
                .show();
        });
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        Log.d(TAG, "💀💀💀 ACTIVITY DESTROYED 💀💀💀");

        // Clean up WebSocket connection
        if (webSocket != null) {
            webSocket.close(1000, "Activity destroyed");
            webSocket = null;
        }

        // Clean up HTTP client
        if (httpClient != null) {
            httpClient.dispatcher().executorService().shutdown();
        }
    }
}