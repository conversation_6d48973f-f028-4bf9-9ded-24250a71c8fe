package com.gamegards.gaming27;

import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.RadioGroup;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class Web3Manager extends AppCompatActivity {

    private static final String TAG = "Web3Manager";

    // UI Components
    private Button btnConnectTrust;
    private Button btnConnectMetaMask;
    private Button btnDisconnectWallet;
    private Button btnGetBalance;
    private Button btnSwitchNetwork;
    private TextView tvWalletAddress;
    private TextView tvConnectionStatus;
    private TextView tvNetworkInfo;
    private TextView tvBalance;
    private RadioGroup rgNetwork;

    // Wallet Configuration
    private String trustWalletPackage = "com.wallet.crypto.trustapp";
    private String metaMaskPackage = "io.metamask";

    // Connection state
    private String currentWalletAddress = "";
    private String currentChainId = "eip155:11155111"; // Default to Sepolia
    private boolean isConnected = false;

    // Network configurations
    private Map<String, NetworkConfig> networks;

    private static class NetworkConfig {
        String chainId;
        String name;
        String rpcUrl;

        NetworkConfig(String chainId, String name, String rpcUrl) {
            this.chainId = chainId;
            this.name = name;
            this.rpcUrl = rpcUrl;
        }
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_web3_manager);
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });

        initializeNetworks();
        initializeViews();
        setupClickListeners();
        updateConnectionStatus();

        // Show initial status
        tvNetworkInfo.setText("Network: Ready for connection");
        Toast.makeText(this, "Web3 Manager Ready", Toast.LENGTH_SHORT).show();
    }

    private void initializeNetworks() {
        networks = new HashMap<>();
        networks.put("sepolia", new NetworkConfig("eip155:11155111", "Sepolia Testnet", "https://sepolia.infura.io/v3/"));
        networks.put("mainnet", new NetworkConfig("eip155:1", "Ethereum Mainnet", "https://mainnet.infura.io/v3/"));
        networks.put("polygon", new NetworkConfig("eip155:137", "Polygon Mainnet", "https://polygon-rpc.com/"));
    }

    private void initializeViews() {
        btnConnectTrust = findViewById(R.id.btnConnectTrust);
        btnConnectMetaMask = findViewById(R.id.btnConnectMetaMask);
        btnDisconnectWallet = findViewById(R.id.btnDisconnectWallet);
        btnGetBalance = findViewById(R.id.btnGetBalance);
        btnSwitchNetwork = findViewById(R.id.btnSwitchNetwork);
        tvWalletAddress = findViewById(R.id.tvWalletAddress);
        tvConnectionStatus = findViewById(R.id.tvConnectionStatus);
        tvNetworkInfo = findViewById(R.id.tvNetworkInfo);
        tvBalance = findViewById(R.id.tvBalance);
        rgNetwork = findViewById(R.id.rgNetwork);
    }



    private void setupClickListeners() {
        btnConnectTrust.setOnClickListener(v -> connectToWallet("trust"));
        btnConnectMetaMask.setOnClickListener(v -> connectToWallet("metamask"));
        btnDisconnectWallet.setOnClickListener(v -> disconnectWallet());
        btnGetBalance.setOnClickListener(v -> getWalletBalance());
        btnSwitchNetwork.setOnClickListener(v -> switchNetwork());

        rgNetwork.setOnCheckedChangeListener((group, checkedId) -> {
            if (checkedId == R.id.rbSepolia) {
                currentChainId = "eip155:11155111";
            } else if (checkedId == R.id.rbMainnet) {
                currentChainId = "eip155:1";
            } else if (checkedId == R.id.rbPolygon) {
                currentChainId = "eip155:137";
            }
            updateNetworkInfo();
        });
    }

    private void connectToWallet(String walletType) {
        if (isConnected) {
            Toast.makeText(this, "Wallet already connected. Disconnect first.", Toast.LENGTH_SHORT).show();
            return;
        }

        Log.d(TAG, "Attempting to connect to " + walletType + " wallet");

        // Check if the wallet app is installed
        String packageName = walletType.equals("trust") ? trustWalletPackage : metaMaskPackage;
        if (!isWalletInstalled(packageName)) {
            showWalletNotInstalledDialog(walletType);
            return;
        }

        // For this simplified version, we'll simulate a connection
        // In a real implementation, you would integrate with WalletConnect or use deep links
        simulateWalletConnection(walletType);
    }

    private void simulateWalletConnection(String walletType) {
        // Show connecting status
        Toast.makeText(this, "Connecting to " + (walletType.equals("trust") ? "Trust Wallet" : "MetaMask") + "...", Toast.LENGTH_SHORT).show();

        // Try to open the wallet app
        try {
            Intent intent;
            if (walletType.equals("trust")) {
                // Try to open Trust Wallet
                intent = getPackageManager().getLaunchIntentForPackage(trustWalletPackage);
                if (intent == null) {
                    // Fallback to deep link
                    intent = new Intent(Intent.ACTION_VIEW, Uri.parse("trust://"));
                }
            } else {
                // Try to open MetaMask
                intent = getPackageManager().getLaunchIntentForPackage(metaMaskPackage);
                if (intent == null) {
                    // Fallback to deep link
                    intent = new Intent(Intent.ACTION_VIEW, Uri.parse("metamask://"));
                }
            }

            if (intent != null) {
                startActivity(intent);

                // Simulate successful connection after a delay
                new Handler(Looper.getMainLooper()).postDelayed(() -> {
                    simulateSuccessfulConnection(walletType);
                }, 3000);
            } else {
                Toast.makeText(this, "Could not open " + walletType + " wallet", Toast.LENGTH_SHORT).show();
            }

        } catch (Exception e) {
            Log.e(TAG, "Error opening wallet: " + e.getMessage());
            Toast.makeText(this, "Error opening wallet: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }

    private void simulateSuccessfulConnection(String walletType) {
        // Simulate a successful connection with a demo address
        String demoAddress = "******************************************";
        currentWalletAddress = demoAddress;
        isConnected = true;

        // Update UI
        tvWalletAddress.setText("Wallet: " + formatAddress(currentWalletAddress));
        tvWalletAddress.setVisibility(View.VISIBLE);
        btnDisconnectWallet.setVisibility(View.VISIBLE);

        updateConnectionStatus();
        Toast.makeText(this, walletType.equals("trust") ? "Trust Wallet Connected!" : "MetaMask Connected!", Toast.LENGTH_SHORT).show();

        // Simulate balance
        tvBalance.setText("0.1234 ETH");
    }

    private boolean isWalletInstalled(String packageName) {
        try {
            getPackageManager().getPackageInfo(packageName, PackageManager.GET_ACTIVITIES);
            return true;
        } catch (PackageManager.NameNotFoundException e) {
            return false;
        }
    }

    private void showWalletNotInstalledDialog(String walletType) {
        String walletName = walletType.equals("trust") ? "Trust Wallet" : "MetaMask";
        String playStoreUrl = walletType.equals("trust") ?
            "https://play.google.com/store/apps/details?id=com.wallet.crypto.trustapp" :
            "https://play.google.com/store/apps/details?id=io.metamask";

        new androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle(walletName + " Not Installed")
            .setMessage(walletName + " is not installed on your device. Would you like to install it from Google Play Store?")
            .setPositiveButton("Install", (dialog, which) -> {
                Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(playStoreUrl));
                startActivity(intent);
            })
            .setNegativeButton("Cancel", null)
            .show();
    }



    private void disconnectWallet() {
        if (!isConnected) {
            Toast.makeText(this, "No wallet connected", Toast.LENGTH_SHORT).show();
            return;
        }

        // Reset connection state
        currentWalletAddress = "";
        isConnected = false;

        // Update UI
        tvWalletAddress.setText("Wallet: Not Connected");
        tvWalletAddress.setVisibility(View.GONE);
        btnDisconnectWallet.setVisibility(View.GONE);
        tvBalance.setText("0.0000 ETH");

        updateConnectionStatus();
        Toast.makeText(this, "Wallet Disconnected", Toast.LENGTH_SHORT).show();
    }

    private void getWalletBalance() {
        if (!isConnected || currentWalletAddress.isEmpty()) {
            Toast.makeText(this, "Please connect wallet first", Toast.LENGTH_SHORT).show();
            return;
        }

        // This is a simplified balance check - in a real app you'd use Web3j or similar
        // For now, we'll just show a placeholder
        tvBalance.setText("Loading...");

        // Simulate balance loading
        new Handler(Looper.getMainLooper()).postDelayed(() -> {
            // In a real implementation, you would:
            // 1. Use Web3j to connect to the network
            // 2. Call eth_getBalance with the wallet address
            // 3. Convert wei to ETH and display

            tvBalance.setText("0.0000 ETH"); // Placeholder
            Toast.makeText(this, "Balance updated (demo)", Toast.LENGTH_SHORT).show();
        }, 2000);
    }

    private void switchNetwork() {
        if (!isConnected) {
            Toast.makeText(this, "Please connect wallet first", Toast.LENGTH_SHORT).show();
            return;
        }

        // Get selected network
        String selectedNetwork = getSelectedNetwork();
        NetworkConfig networkConfig = networks.get(selectedNetwork);

        if (networkConfig == null) {
            Toast.makeText(this, "Invalid network selected", Toast.LENGTH_SHORT).show();
            return;
        }

        // In a real implementation, you would send a wallet_switchEthereumChain request
        // For now, just update the UI
        currentChainId = networkConfig.chainId;
        updateNetworkInfo();
        Toast.makeText(this, "Network switched to " + networkConfig.name, Toast.LENGTH_SHORT).show();
    }

    private String getSelectedNetwork() {
        int selectedId = rgNetwork.getCheckedRadioButtonId();
        if (selectedId == R.id.rbSepolia) {
            return "sepolia";
        } else if (selectedId == R.id.rbMainnet) {
            return "mainnet";
        } else if (selectedId == R.id.rbPolygon) {
            return "polygon";
        }
        return "sepolia"; // default
    }

    private void updateConnectionStatus() {
        if (isConnected) {
            tvConnectionStatus.setText("🟢 Connected");
            tvConnectionStatus.setTextColor(getResources().getColor(android.R.color.holo_green_dark));
        } else {
            tvConnectionStatus.setText("🔴 Disconnected");
            tvConnectionStatus.setTextColor(getResources().getColor(android.R.color.holo_red_dark));
        }
    }

    private void updateNetworkInfo() {
        String selectedNetwork = getSelectedNetwork();
        NetworkConfig networkConfig = networks.get(selectedNetwork);
        if (networkConfig != null) {
            tvNetworkInfo.setText("Network: " + networkConfig.name);
        }
    }

    private String formatAddress(String address) {
        if (address == null || address.length() < 10) {
            return address;
        }
        return address.substring(0, 6) + "..." + address.substring(address.length() - 4);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        // Clean up any resources if needed
    }
}