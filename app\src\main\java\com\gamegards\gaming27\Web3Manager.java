package com.gamegards.gaming27;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

import com.walletconnect.sign.client.Sign;
import com.walletconnect.sign.client.SignClient;

import java.util.Arrays;
import java.util.List;

public class Web3Manager extends AppCompatActivity {

    private Button btnConnectTrust;
    private TextView tvWalletAddress;
    private String trustWalletPackage = "com.wallet.crypto.trustapp";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_web3_manager);
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });

        btnConnectTrust = findViewById(R.id.btnConnectTrust);
        tvWalletAddress = findViewById(R.id.tvWalletAddress);

        btnConnectTrust.setOnClickListener(v -> connectToTrustWallet());
    }

    private void connectToTrustWallet() {
        // Supported chains: Ethereum Mainnet (1), Sepolia (11155111)
        List<String> chains = Arrays.asList("eip155:1", "eip155:11155111");
        List<String> methods = Arrays.asList("eth_sendTransaction", "personal_sign", "eth_signTypedData");

        Sign.Params.Connect connectParams = new Sign.Params.Connect(
                new Sign.Model.SessionProposal(
                        chains,
                        methods,
                        null
                )
        );

        SignClient.connect(connectParams, new Sign.Listeners.Session() {
            @Override
            public void onSessionApproved(Sign.Model.ApprovedSession session) {
                runOnUiThread(() -> {
                    String address = session.getAccounts().isEmpty() ? "Unknown" : session.getAccounts().get(0);
                    tvWalletAddress.setText("Wallet: " + address);
                    tvWalletAddress.setVisibility(View.VISIBLE);
                    Toast.makeText(Web3Manager.this, "Wallet Connected!", Toast.LENGTH_SHORT).show();
                });
            }

            @Override
            public void onSessionRejected(Sign.Model.RejectedSession session) {
                runOnUiThread(() -> Toast.makeText(Web3Manager.this, "Connection rejected", Toast.LENGTH_SHORT).show());
            }

            @Override
            public void onSessionUpdate(Sign.Model.UpdatedSession session) {}

            @Override
            public void onSessionDelete(Sign.Model.DeletedSession session) {
                runOnUiThread(() -> {
                    tvWalletAddress.setText("Wallet: Not Connected");
                    tvWalletAddress.setVisibility(View.GONE);
                    Toast.makeText(Web3Manager.this, "Wallet Disconnected", Toast.LENGTH_SHORT).show();
                });
            }
        });

        // Get the connection URI
        String wcUri = SignClient.getSessionUri();
        if (wcUri != null) {
            Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse("trust://wc?uri=" + Uri.encode(wcUri)));
            intent.setPackage(trustWalletPackage);
            try {
                startActivity(intent);
            } catch (Exception e) {
                Toast.makeText(this, "Trust Wallet not installed", Toast.LENGTH_SHORT).show();
            }
        } else {
            Toast.makeText(this, "Failed to create WalletConnect session", Toast.LENGTH_SHORT).show();
        }
    }
}