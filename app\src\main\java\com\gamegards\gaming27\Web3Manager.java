package com.gamegards.gaming27;

import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.RadioGroup;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

import com.walletconnect.sign.client.Sign;
import com.walletconnect.sign.client.SignClient;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class Web3Manager extends AppCompatActivity {

    private static final String TAG = "Web3Manager";

    // UI Components
    private Button btnConnectTrust;
    private Button btnConnectMetaMask;
    private Button btnDisconnectWallet;
    private Button btnGetBalance;
    private Button btnSwitchNetwork;
    private TextView tvWalletAddress;
    private TextView tvConnectionStatus;
    private TextView tvNetworkInfo;
    private TextView tvBalance;
    private RadioGroup rgNetwork;

    // Wallet Configuration
    private String trustWalletPackage = WalletConnectConfig.TRUST_WALLET_PACKAGE;
    private String metaMaskPackage = WalletConnectConfig.METAMASK_PACKAGE;

    // WalletConnect Configuration
    private String projectId = "2f05a7cac472323a57e1a9b7b2d8e8c1"; // Demo project ID - replace with yours
    private String currentWalletAddress = "";
    private String currentChainId = "eip155:11155111"; // Default to Sepolia
    private boolean isConnected = false;

    // Network configurations
    private Map<String, NetworkConfig> networks;

    private static class NetworkConfig {
        String chainId;
        String name;
        String rpcUrl;

        NetworkConfig(String chainId, String name, String rpcUrl) {
            this.chainId = chainId;
            this.name = name;
            this.rpcUrl = rpcUrl;
        }
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_web3_manager);
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });

        initializeNetworks();
        initializeViews();
        initializeWalletConnect();
        setupClickListeners();
        updateConnectionStatus();
    }

    private void initializeNetworks() {
        networks = new HashMap<>();
        networks.put("sepolia", new NetworkConfig(WalletConnectConfig.SEPOLIA_TESTNET, "Sepolia Testnet", WalletConnectConfig.SEPOLIA_RPC));
        networks.put("mainnet", new NetworkConfig(WalletConnectConfig.ETHEREUM_MAINNET, "Ethereum Mainnet", WalletConnectConfig.ETHEREUM_RPC));
        networks.put("polygon", new NetworkConfig(WalletConnectConfig.POLYGON_MAINNET, "Polygon Mainnet", WalletConnectConfig.POLYGON_RPC));
    }

    private void initializeViews() {
        btnConnectTrust = findViewById(R.id.btnConnectTrust);
        btnConnectMetaMask = findViewById(R.id.btnConnectMetaMask);
        btnDisconnectWallet = findViewById(R.id.btnDisconnectWallet);
        btnGetBalance = findViewById(R.id.btnGetBalance);
        btnSwitchNetwork = findViewById(R.id.btnSwitchNetwork);
        tvWalletAddress = findViewById(R.id.tvWalletAddress);
        tvConnectionStatus = findViewById(R.id.tvConnectionStatus);
        tvNetworkInfo = findViewById(R.id.tvNetworkInfo);
        tvBalance = findViewById(R.id.tvBalance);
        rgNetwork = findViewById(R.id.rgNetwork);
    }

    private void initializeWalletConnect() {
        try {
            // Initialize WalletConnect SignClient
            Sign.Params.Init initParams = new Sign.Params.Init(
                getApplication(),
                projectId,
                WalletConnectConfig.APP_URL,
                WalletConnectConfig.APP_NAME,
                WalletConnectConfig.APP_DESCRIPTION,
                Arrays.asList(WalletConnectConfig.APP_ICON),
                WalletConnectConfig.APP_URL
            );

            SignClient.initialize(initParams, new Sign.Listeners.Init() {
                @Override
                public void onSuccess() {
                    Log.d(TAG, "WalletConnect initialized successfully");
                    runOnUiThread(() -> {
                        tvNetworkInfo.setText("Network: Ready");
                        Toast.makeText(Web3Manager.this, "WalletConnect Ready", Toast.LENGTH_SHORT).show();
                    });
                }

                @Override
                public void onError(Sign.Model.Error error) {
                    Log.e(TAG, "WalletConnect initialization failed: " + error.getThrowable().getMessage());
                    runOnUiThread(() -> {
                        tvNetworkInfo.setText("Network: Error");
                        Toast.makeText(Web3Manager.this, "WalletConnect initialization failed", Toast.LENGTH_SHORT).show();
                    });
                }
            });
        } catch (Exception e) {
            Log.e(TAG, "Error initializing WalletConnect: " + e.getMessage());
            Toast.makeText(this, "Failed to initialize WalletConnect", Toast.LENGTH_SHORT).show();
        }
    }

    private void setupClickListeners() {
        btnConnectTrust.setOnClickListener(v -> connectToWallet("trust"));
        btnConnectMetaMask.setOnClickListener(v -> connectToWallet("metamask"));
        btnDisconnectWallet.setOnClickListener(v -> disconnectWallet());
        btnGetBalance.setOnClickListener(v -> getWalletBalance());
        btnSwitchNetwork.setOnClickListener(v -> switchNetwork());

        rgNetwork.setOnCheckedChangeListener((group, checkedId) -> {
            if (checkedId == R.id.rbSepolia) {
                currentChainId = "eip155:11155111";
            } else if (checkedId == R.id.rbMainnet) {
                currentChainId = "eip155:1";
            } else if (checkedId == R.id.rbPolygon) {
                currentChainId = "eip155:137";
            }
            updateNetworkInfo();
        });
    }

    private void connectToWallet(String walletType) {
        if (isConnected) {
            Toast.makeText(this, "Wallet already connected. Disconnect first.", Toast.LENGTH_SHORT).show();
            return;
        }

        Log.d(TAG, "Attempting to connect to " + walletType + " wallet");

        // Check if the wallet app is installed
        String packageName = walletType.equals("trust") ? trustWalletPackage : metaMaskPackage;
        if (!isWalletInstalled(packageName)) {
            showWalletNotInstalledDialog(walletType);
            return;
        }

        // Supported chains and methods
        List<String> chains = Arrays.asList(
            WalletConnectConfig.ETHEREUM_MAINNET,
            WalletConnectConfig.SEPOLIA_TESTNET,
            WalletConnectConfig.POLYGON_MAINNET,
            WalletConnectConfig.BSC_MAINNET
        );

        List<String> methods = Arrays.asList(WalletConnectConfig.SUPPORTED_METHODS);
        List<String> events = Arrays.asList(WalletConnectConfig.SUPPORTED_EVENTS);

        try {
            // Create session proposal
            Sign.Model.SessionProposal sessionProposal = new Sign.Model.SessionProposal(
                chains,
                methods,
                events
            );

            Sign.Params.Connect connectParams = new Sign.Params.Connect(sessionProposal);

            SignClient.connect(connectParams, new Sign.Listeners.Session() {
                @Override
                public void onSessionApproved(Sign.Model.ApprovedSession session) {
                    Log.d(TAG, "Session approved: " + session.getTopic());
                    runOnUiThread(() -> {
                        handleSessionApproved(session);
                    });
                }

                @Override
                public void onSessionRejected(Sign.Model.RejectedSession session) {
                    Log.d(TAG, "Session rejected: " + session.getReason());
                    runOnUiThread(() -> {
                        handleSessionRejected(session);
                    });
                }

                @Override
                public void onSessionUpdate(Sign.Model.UpdatedSession session) {
                    Log.d(TAG, "Session updated: " + session.getTopic());
                    runOnUiThread(() -> {
                        handleSessionUpdate(session);
                    });
                }

                @Override
                public void onSessionDelete(Sign.Model.DeletedSession session) {
                    Log.d(TAG, "Session deleted: " + session.getTopic());
                    runOnUiThread(() -> {
                        handleSessionDelete(session);
                    });
                }
            });

            // Get the connection URI and open wallet
            new Handler(Looper.getMainLooper()).postDelayed(() -> {
                String wcUri = SignClient.getSessionUri();
                if (wcUri != null && !wcUri.isEmpty()) {
                    openWalletApp(walletType, wcUri);
                } else {
                    Toast.makeText(Web3Manager.this, "Failed to create WalletConnect session", Toast.LENGTH_SHORT).show();
                }
            }, 1000); // Small delay to ensure URI is generated

        } catch (Exception e) {
            Log.e(TAG, "Error connecting to wallet: " + e.getMessage());
            Toast.makeText(this, "Failed to connect: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }

    private boolean isWalletInstalled(String packageName) {
        try {
            getPackageManager().getPackageInfo(packageName, PackageManager.GET_ACTIVITIES);
            return true;
        } catch (PackageManager.NameNotFoundException e) {
            return false;
        }
    }

    private void showWalletNotInstalledDialog(String walletType) {
        String walletName = walletType.equals("trust") ? "Trust Wallet" : "MetaMask";
        String playStoreUrl = walletType.equals("trust") ?
            WalletConnectConfig.TRUST_WALLET_PLAY_STORE :
            WalletConnectConfig.METAMASK_PLAY_STORE;

        new androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle(walletName + " Not Installed")
            .setMessage(walletName + " is not installed on your device. Would you like to install it from Google Play Store?")
            .setPositiveButton("Install", (dialog, which) -> {
                Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(playStoreUrl));
                startActivity(intent);
            })
            .setNegativeButton("Cancel", null)
            .show();
    }

    private void openWalletApp(String walletType, String wcUri) {
        try {
            Intent intent;
            if (walletType.equals("trust")) {
                // Trust Wallet deep link
                intent = new Intent(Intent.ACTION_VIEW, Uri.parse(WalletConnectConfig.TRUST_WALLET_SCHEME + Uri.encode(wcUri)));
                intent.setPackage(trustWalletPackage);
            } else {
                // MetaMask deep link
                intent = new Intent(Intent.ACTION_VIEW, Uri.parse(WalletConnectConfig.METAMASK_SCHEME + Uri.encode(wcUri)));
                intent.setPackage(metaMaskPackage);
            }

            startActivity(intent);
            Toast.makeText(this, "Opening " + (walletType.equals("trust") ? "Trust Wallet" : "MetaMask") + "...", Toast.LENGTH_SHORT).show();

        } catch (Exception e) {
            Log.e(TAG, "Error opening wallet app: " + e.getMessage());
            // Fallback: try to open with generic intent
            try {
                Intent fallbackIntent = new Intent(Intent.ACTION_VIEW, Uri.parse(wcUri));
                startActivity(fallbackIntent);
            } catch (Exception fallbackError) {
                Toast.makeText(this, "Failed to open wallet app", Toast.LENGTH_SHORT).show();
            }
        }
    }

    private void handleSessionApproved(Sign.Model.ApprovedSession session) {
        Log.d(TAG, "Wallet connected successfully");

        if (!session.getAccounts().isEmpty()) {
            currentWalletAddress = session.getAccounts().get(0);
            isConnected = true;

            // Update UI
            tvWalletAddress.setText("Wallet: " + formatAddress(currentWalletAddress));
            tvWalletAddress.setVisibility(View.VISIBLE);
            btnDisconnectWallet.setVisibility(View.VISIBLE);

            updateConnectionStatus();
            Toast.makeText(this, "Wallet Connected Successfully!", Toast.LENGTH_SHORT).show();

            // Automatically get balance
            getWalletBalance();
        } else {
            Toast.makeText(this, "No accounts found in wallet", Toast.LENGTH_SHORT).show();
        }
    }

    private void handleSessionRejected(Sign.Model.RejectedSession session) {
        Log.d(TAG, "Connection rejected by user");
        Toast.makeText(this, "Connection rejected by user", Toast.LENGTH_SHORT).show();
        updateConnectionStatus();
    }

    private void handleSessionUpdate(Sign.Model.UpdatedSession session) {
        Log.d(TAG, "Session updated");
        // Handle session updates if needed
        Toast.makeText(this, "Session updated", Toast.LENGTH_SHORT).show();
    }

    private void handleSessionDelete(Sign.Model.DeletedSession session) {
        Log.d(TAG, "Session deleted - wallet disconnected");

        currentWalletAddress = "";
        isConnected = false;

        // Update UI
        tvWalletAddress.setText("Wallet: Not Connected");
        tvWalletAddress.setVisibility(View.GONE);
        btnDisconnectWallet.setVisibility(View.GONE);
        tvBalance.setText("0.0000 ETH");

        updateConnectionStatus();
        Toast.makeText(this, "Wallet Disconnected", Toast.LENGTH_SHORT).show();
    }

    private void disconnectWallet() {
        if (!isConnected) {
            Toast.makeText(this, "No wallet connected", Toast.LENGTH_SHORT).show();
            return;
        }

        try {
            // Get active sessions and disconnect them
            List<Sign.Model.Session> sessions = SignClient.getActiveSessions();
            for (Sign.Model.Session session : sessions) {
                Sign.Params.Disconnect disconnectParams = new Sign.Params.Disconnect(
                    session.getTopic(),
                    new Sign.Model.SessionDelete("User requested disconnect", 6000)
                );

                SignClient.disconnect(disconnectParams, new Sign.Listeners.Session() {
                    @Override
                    public void onSessionApproved(Sign.Model.ApprovedSession session) {}

                    @Override
                    public void onSessionRejected(Sign.Model.RejectedSession session) {}

                    @Override
                    public void onSessionUpdate(Sign.Model.UpdatedSession session) {}

                    @Override
                    public void onSessionDelete(Sign.Model.DeletedSession session) {
                        runOnUiThread(() -> handleSessionDelete(session));
                    }
                });
            }

            if (sessions.isEmpty()) {
                // No active sessions, just reset UI
                handleSessionDelete(null);
            }

        } catch (Exception e) {
            Log.e(TAG, "Error disconnecting wallet: " + e.getMessage());
            Toast.makeText(this, "Error disconnecting wallet", Toast.LENGTH_SHORT).show();
        }
    }

    private void getWalletBalance() {
        if (!isConnected || currentWalletAddress.isEmpty()) {
            Toast.makeText(this, "Please connect wallet first", Toast.LENGTH_SHORT).show();
            return;
        }

        // This is a simplified balance check - in a real app you'd use Web3j or similar
        // For now, we'll just show a placeholder
        tvBalance.setText("Loading...");

        // Simulate balance loading
        new Handler(Looper.getMainLooper()).postDelayed(() -> {
            // In a real implementation, you would:
            // 1. Use Web3j to connect to the network
            // 2. Call eth_getBalance with the wallet address
            // 3. Convert wei to ETH and display

            tvBalance.setText("0.0000 ETH"); // Placeholder
            Toast.makeText(this, "Balance updated (demo)", Toast.LENGTH_SHORT).show();
        }, 2000);
    }

    private void switchNetwork() {
        if (!isConnected) {
            Toast.makeText(this, "Please connect wallet first", Toast.LENGTH_SHORT).show();
            return;
        }

        // Get selected network
        String selectedNetwork = getSelectedNetwork();
        NetworkConfig networkConfig = networks.get(selectedNetwork);

        if (networkConfig == null) {
            Toast.makeText(this, "Invalid network selected", Toast.LENGTH_SHORT).show();
            return;
        }

        // In a real implementation, you would send a wallet_switchEthereumChain request
        // For now, just update the UI
        currentChainId = networkConfig.chainId;
        updateNetworkInfo();
        Toast.makeText(this, "Network switched to " + networkConfig.name, Toast.LENGTH_SHORT).show();
    }

    private String getSelectedNetwork() {
        int selectedId = rgNetwork.getCheckedRadioButtonId();
        if (selectedId == R.id.rbSepolia) {
            return "sepolia";
        } else if (selectedId == R.id.rbMainnet) {
            return "mainnet";
        } else if (selectedId == R.id.rbPolygon) {
            return "polygon";
        }
        return "sepolia"; // default
    }

    private void updateConnectionStatus() {
        if (isConnected) {
            tvConnectionStatus.setText("🟢 Connected");
            tvConnectionStatus.setTextColor(getResources().getColor(android.R.color.holo_green_dark));
        } else {
            tvConnectionStatus.setText("🔴 Disconnected");
            tvConnectionStatus.setTextColor(getResources().getColor(android.R.color.holo_red_dark));
        }
    }

    private void updateNetworkInfo() {
        String selectedNetwork = getSelectedNetwork();
        NetworkConfig networkConfig = networks.get(selectedNetwork);
        if (networkConfig != null) {
            tvNetworkInfo.setText("Network: " + networkConfig.name);
        }
    }

    private String formatAddress(String address) {
        if (address == null || address.length() < 10) {
            return address;
        }
        return address.substring(0, 6) + "..." + address.substring(address.length() - 4);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        // Clean up any resources if needed
    }
}