package com.gamegards.gaming27;

import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.RadioGroup;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.WebSocket;
import okhttp3.WebSocketListener;

public class Web3Manager extends AppCompatActivity {

    private static final String TAG = "Web3Manager";

    // UI Components
    private Button btnConnectTrust;
    private Button btnConnectMetaMask;
    private Button btnDisconnectWallet;
    private Button btnGetBalance;
    private Button btnSwitchNetwork;
    private TextView tvWalletAddress;
    private TextView tvConnectionStatus;
    private TextView tvNetworkInfo;
    private TextView tvBalance;
    private RadioGroup rgNetwork;

    // Wallet Configuration
    private String trustWalletPackage = "com.wallet.crypto.trustapp";
    private String metaMaskPackage = "io.metamask";

    // Connection state
    private String currentWalletAddress = "";
    private String currentChainId = "eip155:11155111"; // Default to Sepolia
    private boolean isConnected = false;

    // WalletConnect
    private WebSocket webSocket;
    private OkHttpClient httpClient;
    private String sessionTopic = "";
    private String bridgeUrl = "https://bridge.walletconnect.org"; // Changed to HTTPS
    private String wcUri = "";

    // Network configurations
    private Map<String, NetworkConfig> networks;

    private static class NetworkConfig {
        String chainId;
        String name;
        String rpcUrl;

        NetworkConfig(String chainId, String name, String rpcUrl) {
            this.chainId = chainId;
            this.name = name;
            this.rpcUrl = rpcUrl;
        }
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_web3_manager);
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });

        initializeNetworks();
        initializeViews();
        initializeWalletConnect();
        setupClickListeners();
        updateConnectionStatus();

        // Show initial status
        tvNetworkInfo.setText("Network: Ready for connection");
        Toast.makeText(this, "Web3 Manager Ready", Toast.LENGTH_SHORT).show();
    }

    private void initializeNetworks() {
        networks = new HashMap<>();
        networks.put("sepolia", new NetworkConfig("eip155:11155111", "Sepolia Testnet", "https://sepolia.infura.io/v3/"));
        networks.put("mainnet", new NetworkConfig("eip155:1", "Ethereum Mainnet", "https://mainnet.infura.io/v3/"));
        networks.put("polygon", new NetworkConfig("eip155:137", "Polygon Mainnet", "https://polygon-rpc.com/"));
    }

    private void initializeWalletConnect() {
        httpClient = new OkHttpClient.Builder().build();
        Log.d(TAG, "WalletConnect HTTP client initialized");
    }

    private void initializeViews() {
        btnConnectTrust = findViewById(R.id.btnConnectTrust);
        btnConnectMetaMask = findViewById(R.id.btnConnectMetaMask);
        btnDisconnectWallet = findViewById(R.id.btnDisconnectWallet);
        btnGetBalance = findViewById(R.id.btnGetBalance);
        btnSwitchNetwork = findViewById(R.id.btnSwitchNetwork);
        tvWalletAddress = findViewById(R.id.tvWalletAddress);
        tvConnectionStatus = findViewById(R.id.tvConnectionStatus);
        tvNetworkInfo = findViewById(R.id.tvNetworkInfo);
        tvBalance = findViewById(R.id.tvBalance);
        rgNetwork = findViewById(R.id.rgNetwork);
    }



    private void setupClickListeners() {
        btnConnectTrust.setOnClickListener(v -> connectToWallet("trust"));
        btnConnectMetaMask.setOnClickListener(v -> connectToWallet("metamask"));
        btnDisconnectWallet.setOnClickListener(v -> disconnectWallet());
        btnGetBalance.setOnClickListener(v -> getWalletBalance());
        btnSwitchNetwork.setOnClickListener(v -> switchNetwork());

        rgNetwork.setOnCheckedChangeListener((group, checkedId) -> {
            if (checkedId == R.id.rbSepolia) {
                currentChainId = "eip155:11155111";
            } else if (checkedId == R.id.rbMainnet) {
                currentChainId = "eip155:1";
            } else if (checkedId == R.id.rbPolygon) {
                currentChainId = "eip155:137";
            }
            updateNetworkInfo();
        });
    }

    private void connectToWallet(String walletType) {
        if (isConnected) {
            Toast.makeText(this, "Wallet already connected. Disconnect first.", Toast.LENGTH_SHORT).show();
            return;
        }

        Log.d(TAG, "Attempting to connect to " + walletType + " wallet");

        // Check if the wallet app is installed
        String packageName = walletType.equals("trust") ? trustWalletPackage : metaMaskPackage;
        if (!isWalletInstalled(packageName)) {
            showWalletNotInstalledDialog(walletType);
            return;
        }

        // Create real WalletConnect session
        createWalletConnectSession(walletType);
    }

    private void createWalletConnectSession(String walletType) {
        Log.d(TAG, "=== Starting WalletConnect Session Creation ===");
        Toast.makeText(this, "Creating WalletConnect session...", Toast.LENGTH_SHORT).show();

        try {
            // Generate session parameters
            String sessionId = UUID.randomUUID().toString();
            sessionTopic = sessionId;
            Log.d(TAG, "Generated session ID: " + sessionId);

            // Create WalletConnect URI
            JSONObject sessionRequest = new JSONObject();
            sessionRequest.put("id", sessionId);
            sessionRequest.put("jsonrpc", "2.0");
            sessionRequest.put("method", "wc_sessionRequest");

            JSONObject params = new JSONObject();
            params.put("peerId", UUID.randomUUID().toString());
            params.put("peerMeta", createPeerMeta());
            params.put("chainId", 1); // Ethereum mainnet

            sessionRequest.put("params", new JSONArray().put(params));
            Log.d(TAG, "Created session request: " + sessionRequest.toString());

            // Create WalletConnect URI
            String key = generateKey();
            wcUri = "wc:" + sessionId + "@1" +
                    "?bridge=" + Uri.encode(bridgeUrl) +
                    "&key=" + key;

            Log.d(TAG, "Bridge URL: " + bridgeUrl);
            Log.d(TAG, "Generated key: " + key);
            Log.d(TAG, "Created WalletConnect URI: " + wcUri);

            // Test network connectivity first
            testNetworkConnectivity();

            // Connect to bridge
            connectToBridge(sessionRequest);

            // Open wallet app with WalletConnect URI
            openWalletWithWC(walletType, wcUri);

        } catch (Exception e) {
            Log.e(TAG, "Error creating WalletConnect session: " + e.getMessage());
            e.printStackTrace();
            Toast.makeText(this, "Failed to create session: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }

    private void testNetworkConnectivity() {
        Log.d(TAG, "=== Testing Network Connectivity ===");

        // Test basic HTTP connectivity
        Request testRequest = new Request.Builder()
                .url("https://www.google.com")
                .build();

        httpClient.newCall(testRequest).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                Log.e(TAG, "Network test failed: " + e.getMessage());
                runOnUiThread(() -> {
                    Toast.makeText(Web3Manager.this, "Network connectivity issue: " + e.getMessage(), Toast.LENGTH_LONG).show();
                });
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                Log.d(TAG, "Network test successful. Response code: " + response.code());
                response.close();
            }
        });

        // Test bridge URL specifically
        Request bridgeTestRequest = new Request.Builder()
                .url(bridgeUrl)
                .build();

        httpClient.newCall(bridgeTestRequest).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                Log.e(TAG, "Bridge URL test failed: " + e.getMessage());
                runOnUiThread(() -> {
                    Toast.makeText(Web3Manager.this, "Bridge connection failed: " + e.getMessage(), Toast.LENGTH_LONG).show();
                });
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                Log.d(TAG, "Bridge URL test successful. Response code: " + response.code());
                response.close();
            }
        });
    }

    private JSONObject createPeerMeta() throws JSONException {
        JSONObject peerMeta = new JSONObject();
        peerMeta.put("description", "Gaming27 Web3 Integration");
        peerMeta.put("url", "https://gaming27.com");
        peerMeta.put("icons", new JSONArray().put("https://gaming27.com/icon.png"));
        peerMeta.put("name", "Gaming27");
        return peerMeta;
    }

    private String generateKey() {
        // Generate a random 32-byte key for WalletConnect
        return UUID.randomUUID().toString().replace("-", "") + UUID.randomUUID().toString().replace("-", "").substring(0, 32);
    }

    private void connectToBridge(JSONObject sessionRequest) {
        Log.d(TAG, "=== Connecting to Bridge ===");
        Log.d(TAG, "Bridge URL: " + bridgeUrl);

        // Try multiple bridge URLs as fallback
        String[] bridgeUrls = {
            "https://bridge.walletconnect.org",
            "wss://bridge.walletconnect.org",
            "https://safe-walletconnect.gnosis.io",
            "wss://safe-walletconnect.gnosis.io"
        };

        connectToBridgeWithFallback(sessionRequest, bridgeUrls, 0);
    }

    private void connectToBridgeWithFallback(JSONObject sessionRequest, String[] bridgeUrls, int index) {
        if (index >= bridgeUrls.length) {
            Log.e(TAG, "All bridge URLs failed");
            runOnUiThread(() -> {
                Toast.makeText(Web3Manager.this, "All bridge connections failed. Please check internet connection.", Toast.LENGTH_LONG).show();
            });
            return;
        }

        String currentBridgeUrl = bridgeUrls[index];
        Log.d(TAG, "Trying bridge URL " + (index + 1) + "/" + bridgeUrls.length + ": " + currentBridgeUrl);

        Request request = new Request.Builder()
                .url(currentBridgeUrl)
                .build();

        webSocket = httpClient.newWebSocket(request, new WebSocketListener() {
            @Override
            public void onOpen(WebSocket webSocket, Response response) {
                Log.d(TAG, "✅ WebSocket connected to bridge: " + currentBridgeUrl);
                Log.d(TAG, "Response: " + (response != null ? response.toString() : "null"));
                runOnUiThread(() -> {
                    Toast.makeText(Web3Manager.this, "Connected to bridge!", Toast.LENGTH_SHORT).show();
                });

                // Send session request
                Log.d(TAG, "Sending session request: " + sessionRequest.toString());
                webSocket.send(sessionRequest.toString());
            }

            @Override
            public void onMessage(WebSocket webSocket, String text) {
                Log.d(TAG, "📨 Received message from bridge: " + text);
                handleBridgeMessage(text);
            }

            @Override
            public void onFailure(WebSocket webSocket, Throwable t, Response response) {
                Log.e(TAG, "❌ WebSocket connection failed for " + currentBridgeUrl + ": " + t.getMessage());
                Log.e(TAG, "Response: " + (response != null ? response.toString() : "null"));
                t.printStackTrace();

                // Try next bridge URL
                connectToBridgeWithFallback(sessionRequest, bridgeUrls, index + 1);
            }

            @Override
            public void onClosed(WebSocket webSocket, int code, String reason) {
                Log.d(TAG, "🔒 WebSocket closed. Code: " + code + ", Reason: " + reason);
                runOnUiThread(() -> {
                    Toast.makeText(Web3Manager.this, "Bridge connection closed: " + reason, Toast.LENGTH_SHORT).show();
                });
            }
        });
    }

    private void handleBridgeMessage(String message) {
        Log.d(TAG, "=== Handling Bridge Message ===");
        Log.d(TAG, "Raw message: " + message);

        try {
            JSONObject json = new JSONObject(message);
            String method = json.optString("method");
            Log.d(TAG, "Message method: " + method);

            if ("wc_sessionUpdate".equals(method)) {
                Log.d(TAG, "Processing session update");
                JSONArray params = json.getJSONArray("params");
                Log.d(TAG, "Params: " + params.toString());

                if (params.length() > 0) {
                    JSONObject sessionData = params.getJSONObject(0);
                    boolean approved = sessionData.optBoolean("approved", false);
                    Log.d(TAG, "Session approved: " + approved);

                    if (approved) {
                        JSONArray accounts = sessionData.optJSONArray("accounts");
                        Log.d(TAG, "Accounts: " + (accounts != null ? accounts.toString() : "null"));

                        if (accounts != null && accounts.length() > 0) {
                            String walletAddress = accounts.getString(0);
                            Log.d(TAG, "✅ Wallet address received: " + walletAddress);
                            runOnUiThread(() -> handleWalletConnected(walletAddress));
                        } else {
                            Log.w(TAG, "No accounts found in approved session");
                            runOnUiThread(() -> {
                                Toast.makeText(Web3Manager.this, "No accounts found in wallet", Toast.LENGTH_SHORT).show();
                            });
                        }
                    } else {
                        Log.w(TAG, "Session rejected by wallet");
                        runOnUiThread(() -> {
                            Toast.makeText(Web3Manager.this, "Connection rejected by wallet", Toast.LENGTH_SHORT).show();
                        });
                    }
                } else {
                    Log.w(TAG, "Empty params in session update");
                }
            } else {
                Log.d(TAG, "Unhandled message method: " + method);
                Log.d(TAG, "Full message: " + json.toString());
            }
        } catch (JSONException e) {
            Log.e(TAG, "❌ Error parsing bridge message: " + e.getMessage());
            Log.e(TAG, "Problematic message: " + message);
            e.printStackTrace();
        }
    }

    private void handleWalletConnected(String walletAddress) {
        Log.d(TAG, "=== Wallet Connected Successfully ===");
        Log.d(TAG, "✅ Wallet address: " + walletAddress);

        currentWalletAddress = walletAddress;
        isConnected = true;

        // Update UI
        tvWalletAddress.setText("Wallet: " + formatAddress(currentWalletAddress));
        tvWalletAddress.setVisibility(View.VISIBLE);
        btnDisconnectWallet.setVisibility(View.VISIBLE);

        updateConnectionStatus();
        Toast.makeText(this, "🎉 Wallet Connected Successfully!\nAddress: " + formatAddress(walletAddress), Toast.LENGTH_LONG).show();

        Log.d(TAG, "Starting balance fetch...");
        // Get real balance
        getWalletBalance();
    }

    private void openWalletWithWC(String walletType, String wcUri) {
        Log.d(TAG, "=== Opening Wallet App ===");
        Log.d(TAG, "Wallet type: " + walletType);
        Log.d(TAG, "WC URI: " + wcUri);

        try {
            Intent intent;
            String deepLink;

            if (walletType.equals("trust")) {
                // Trust Wallet deep link with WalletConnect
                deepLink = "trust://wc?uri=" + Uri.encode(wcUri);
                intent = new Intent(Intent.ACTION_VIEW, Uri.parse(deepLink));
                intent.setPackage(trustWalletPackage);
                Log.d(TAG, "Trust Wallet deep link: " + deepLink);
            } else {
                // MetaMask deep link with WalletConnect
                deepLink = "metamask://wc?uri=" + Uri.encode(wcUri);
                intent = new Intent(Intent.ACTION_VIEW, Uri.parse(deepLink));
                intent.setPackage(metaMaskPackage);
                Log.d(TAG, "MetaMask deep link: " + deepLink);
            }

            Log.d(TAG, "Starting wallet activity...");
            startActivity(intent);
            Toast.makeText(this, "🚀 Opening " + (walletType.equals("trust") ? "Trust Wallet" : "MetaMask") + "...", Toast.LENGTH_SHORT).show();

        } catch (Exception e) {
            Log.e(TAG, "❌ Error opening wallet app: " + e.getMessage());
            e.printStackTrace();
            Toast.makeText(this, "Failed to open wallet app: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }

    private boolean isWalletInstalled(String packageName) {
        try {
            getPackageManager().getPackageInfo(packageName, PackageManager.GET_ACTIVITIES);
            return true;
        } catch (PackageManager.NameNotFoundException e) {
            return false;
        }
    }

    private void showWalletNotInstalledDialog(String walletType) {
        String walletName = walletType.equals("trust") ? "Trust Wallet" : "MetaMask";
        String playStoreUrl = walletType.equals("trust") ?
            "https://play.google.com/store/apps/details?id=com.wallet.crypto.trustapp" :
            "https://play.google.com/store/apps/details?id=io.metamask";

        new androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle(walletName + " Not Installed")
            .setMessage(walletName + " is not installed on your device. Would you like to install it from Google Play Store?")
            .setPositiveButton("Install", (dialog, which) -> {
                Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(playStoreUrl));
                startActivity(intent);
            })
            .setNegativeButton("Cancel", null)
            .show();
    }



    private void disconnectWallet() {
        if (!isConnected) {
            Toast.makeText(this, "No wallet connected", Toast.LENGTH_SHORT).show();
            return;
        }

        // Close WebSocket connection
        if (webSocket != null) {
            try {
                // Send session kill request
                JSONObject killRequest = new JSONObject();
                killRequest.put("id", System.currentTimeMillis());
                killRequest.put("jsonrpc", "2.0");
                killRequest.put("method", "wc_sessionKill");
                killRequest.put("params", new JSONArray().put(new JSONObject().put("message", "Session ended by user")));

                webSocket.send(killRequest.toString());
                webSocket.close(1000, "Session ended");
                webSocket = null;
            } catch (Exception e) {
                Log.e(TAG, "Error closing WebSocket: " + e.getMessage());
            }
        }

        // Reset connection state
        currentWalletAddress = "";
        isConnected = false;
        sessionTopic = "";
        wcUri = "";

        // Update UI
        tvWalletAddress.setText("Wallet: Not Connected");
        tvWalletAddress.setVisibility(View.GONE);
        btnDisconnectWallet.setVisibility(View.GONE);
        tvBalance.setText("0.0000 ETH");

        updateConnectionStatus();
        Toast.makeText(this, "Wallet Disconnected", Toast.LENGTH_SHORT).show();
    }

    private void getWalletBalance() {
        Log.d(TAG, "=== Getting Wallet Balance ===");

        if (!isConnected || currentWalletAddress.isEmpty()) {
            Log.w(TAG, "Cannot get balance - wallet not connected");
            Toast.makeText(this, "Please connect wallet first", Toast.LENGTH_SHORT).show();
            return;
        }

        Log.d(TAG, "Getting balance for address: " + currentWalletAddress);
        tvBalance.setText("Loading...");

        // Make real Web3 RPC call to get balance
        try {
            JSONObject rpcRequest = new JSONObject();
            rpcRequest.put("jsonrpc", "2.0");
            rpcRequest.put("method", "eth_getBalance");
            rpcRequest.put("params", new JSONArray().put(currentWalletAddress).put("latest"));
            rpcRequest.put("id", 1);

            Log.d(TAG, "RPC Request: " + rpcRequest.toString());

            // Use Ethereum mainnet RPC
            String rpcUrl = "https://mainnet.infura.io/v3/********************************"; // Public Infura endpoint
            Log.d(TAG, "RPC URL: " + rpcUrl);

            RequestBody body = RequestBody.create(
                rpcRequest.toString(),
                MediaType.parse("application/json")
            );

            Request request = new Request.Builder()
                .url(rpcUrl)
                .post(body)
                .build();

            Log.d(TAG, "Sending balance request...");

            httpClient.newCall(request).enqueue(new Callback() {
                @Override
                public void onFailure(Call call, IOException e) {
                    Log.e(TAG, "❌ Balance request failed: " + e.getMessage());
                    e.printStackTrace();
                    runOnUiThread(() -> {
                        tvBalance.setText("Error loading balance");
                        Toast.makeText(Web3Manager.this, "Failed to get balance: " + e.getMessage(), Toast.LENGTH_LONG).show();
                    });
                }

                @Override
                public void onResponse(Call call, Response response) throws IOException {
                    Log.d(TAG, "📊 Balance response received. Code: " + response.code());

                    try {
                        String responseBody = response.body().string();
                        Log.d(TAG, "Response body: " + responseBody);

                        JSONObject json = new JSONObject(responseBody);

                        if (json.has("result")) {
                            String balanceHex = json.getString("result");
                            Log.d(TAG, "Balance hex: " + balanceHex);

                            // Convert hex to decimal (wei) then to ETH
                            long balanceWei = Long.parseLong(balanceHex.substring(2), 16);
                            double balanceEth = balanceWei / 1e18;

                            Log.d(TAG, "Balance wei: " + balanceWei);
                            Log.d(TAG, "Balance ETH: " + balanceEth);

                            runOnUiThread(() -> {
                                tvBalance.setText(String.format("%.4f ETH", balanceEth));
                                Toast.makeText(Web3Manager.this, "✅ Balance updated: " + String.format("%.4f ETH", balanceEth), Toast.LENGTH_SHORT).show();
                            });
                        } else if (json.has("error")) {
                            JSONObject error = json.getJSONObject("error");
                            String errorMessage = error.optString("message", "Unknown error");
                            Log.e(TAG, "RPC Error: " + errorMessage);

                            runOnUiThread(() -> {
                                tvBalance.setText("RPC Error");
                                Toast.makeText(Web3Manager.this, "RPC Error: " + errorMessage, Toast.LENGTH_LONG).show();
                            });
                        } else {
                            Log.e(TAG, "No result or error in response: " + responseBody);
                            runOnUiThread(() -> {
                                tvBalance.setText("Invalid response");
                                Toast.makeText(Web3Manager.this, "Invalid response from RPC", Toast.LENGTH_SHORT).show();
                            });
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "❌ Error parsing balance response: " + e.getMessage());
                        e.printStackTrace();
                        runOnUiThread(() -> {
                            tvBalance.setText("Parse error");
                            Toast.makeText(Web3Manager.this, "Error parsing balance: " + e.getMessage(), Toast.LENGTH_LONG).show();
                        });
                    }
                }
            });

        } catch (Exception e) {
            Log.e(TAG, "Error getting balance: " + e.getMessage());
            tvBalance.setText("Error");
            Toast.makeText(this, "Error getting balance: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }

    private void switchNetwork() {
        if (!isConnected) {
            Toast.makeText(this, "Please connect wallet first", Toast.LENGTH_SHORT).show();
            return;
        }

        // Get selected network
        String selectedNetwork = getSelectedNetwork();
        NetworkConfig networkConfig = networks.get(selectedNetwork);

        if (networkConfig == null) {
            Toast.makeText(this, "Invalid network selected", Toast.LENGTH_SHORT).show();
            return;
        }

        // In a real implementation, you would send a wallet_switchEthereumChain request
        // For now, just update the UI
        currentChainId = networkConfig.chainId;
        updateNetworkInfo();
        Toast.makeText(this, "Network switched to " + networkConfig.name, Toast.LENGTH_SHORT).show();
    }

    private String getSelectedNetwork() {
        int selectedId = rgNetwork.getCheckedRadioButtonId();
        if (selectedId == R.id.rbSepolia) {
            return "sepolia";
        } else if (selectedId == R.id.rbMainnet) {
            return "mainnet";
        } else if (selectedId == R.id.rbPolygon) {
            return "polygon";
        }
        return "sepolia"; // default
    }

    private void updateConnectionStatus() {
        if (isConnected) {
            tvConnectionStatus.setText("🟢 Connected");
            tvConnectionStatus.setTextColor(getResources().getColor(android.R.color.holo_green_dark));
        } else {
            tvConnectionStatus.setText("🔴 Disconnected");
            tvConnectionStatus.setTextColor(getResources().getColor(android.R.color.holo_red_dark));
        }
    }

    private void updateNetworkInfo() {
        String selectedNetwork = getSelectedNetwork();
        NetworkConfig networkConfig = networks.get(selectedNetwork);
        if (networkConfig != null) {
            tvNetworkInfo.setText("Network: " + networkConfig.name);
        }
    }

    private String formatAddress(String address) {
        if (address == null || address.length() < 10) {
            return address;
        }
        return address.substring(0, 6) + "..." + address.substring(address.length() - 4);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        // Clean up WebSocket connection
        if (webSocket != null) {
            webSocket.close(1000, "Activity destroyed");
            webSocket = null;
        }

        // Clean up HTTP client
        if (httpClient != null) {
            httpClient.dispatcher().executorService().shutdown();
        }
    }
}